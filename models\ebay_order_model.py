from pymongo import MongoClient
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MongoDB Configuration
mongo_uri = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
mongo_dbname = 'test'
client = MongoClient(mongo_uri)
db = client[mongo_dbname]

class EbayOrder:
    def __init__(self):
        self.collection = db['ebayOrders']
        self.collection.create_index([("orderId", 1), ("username", 1)], unique=True)

    def save_order(self, order_data, username):
        """Save order with username, checking for duplicates"""
        order_data['username'] = username
        try:
            # Use update_one with upsert to avoid duplicates
            self.collection.update_one(
                {"orderId": order_data['orderId'], "username": username},
                {"$set": order_data},
                upsert=True
            )
            return True
        except Exception as e:
            print(f"Error saving order: {str(e)}")
            return False

    def get_orders(self, username, limit=50, skip=0):
        """Get orders for a specific user"""
        try:
            return list(self.collection.find(
                {"username": username},
                {'_id': 0}
            ).sort("creationDate", -1).skip(skip).limit(limit))
        except Exception as e:
            print(f"Error getting orders: {str(e)}")
            return []

    def get_order(self, order_id, username):
        """Get specific order"""
        return self.collection.find_one(
            {"orderId": order_id, "username": username},
            {'_id': 0}
        )

    def update_order_status(self, order_id, username, status):
        """Update order status"""
        try:
            self.collection.update_one(
                {"orderId": order_id, "username": username},
                {"$set": {"orderFulfillmentStatus": status}}
            )
            return True
        except Exception as e:
            print(f"Error updating order status: {str(e)}")
            return False
