from mongoengine import Document, <PERSON><PERSON>ield, <PERSON>loatField, DateTimeField, Reference<PERSON>ield
from datetime import datetime
from models.invoice_model import Invoice

class Payment(Document):
    invoice = ReferenceField(Invoice, required=True)
    amount = FloatField(required=True)
    date = DateTimeField(default=datetime.utcnow)
    payment_method = StringField(default='manual')
    notes = StringField()

    meta = {
        'collection': 'payments',
        'ordering': ['-date']
    }
