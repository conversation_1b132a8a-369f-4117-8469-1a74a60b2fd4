.status-indicators {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-left: auto;
}

.status-item {
    display: flex;
    align-items: center;
}

.status-label {
    margin-right: 0.5rem;
}

.status-text {
    color: green;
}

.status-text.red {
    color: red;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-placeholder {
    background-color: #343a40;
    color: white;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 150px;
    font-size: 1.5em;
}

.half-section {
    background-color: transparent;
    color: white;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 300px;
    font-size: 1.5em;
}

.btn-primary {
    background-color: #007bff;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.btn-lg {
    padding: 0.75rem 1.25rem;
    font-size: 1.25rem;
}

.btn-block {
    display: block;
    width: 100%;
}

.card {
    background-color: #343a40;
    border: none;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.15);
}

.card-body {
    padding: 0.25rem;
}

.card-title {
    color: #ffffff;
    margin-bottom: 0.1rem;
    font-size: 0.8rem;
}

.stat-card {
    height: auto;
}

.stat-card .card-text {
    font-size: 1rem;
    margin-bottom: 0;
}

.dropzone {
    border: 2px dashed #ccc;
    border-radius: 5px;
    padding: 2rem;
    text-align: center;
    color: #aaa;
    transition: background-color 0.3s, color 0.3s;
}

.dropzone.dragover {
    background-color: #444;
    color: #fff;
}

.dropzone input {
    display: none;
}

.sample-data {
    margin-top: 1rem;
    width: 100%;
    max-height: 150px;
    overflow: auto;
    background: #fff;
    color: #000;
    border-radius: 5px;
    padding: 0.5rem;
    text-align: left;
}

.sample-data pre {
    margin: 0;
}

.convert-button {
    margin-top: 1rem;
    display: none;
}

.list-group-item {
    background-color: transparent;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    color: #ffffff;
}

#customRuleArea .list-group-item {
    background-color: #343a40 !important;
    color: #ffffff !important;
    border: 1px solid #454d55;
}

#customRuleArea input[type="number"] {
    background-color: #495057;
    color: #ffffff;
    border: 1px solid #6c757d;
}

#customRuleArea input[type="number"]::placeholder {
    color: #adb5bd;
}

.modal-content {
    background-color: #343a40 !important;
    color: #ffffff !important;
}

.modal-header .close {
    color: #ffffff;
}

.form-check-input {
    background-color: #495057;
    border: 1px solid #6c757d;
}

.form-check-label {
    color: #ffffff;
}

/* New Dashboard Styles */
body {
    background-color: #6b21a8; /* Purple background */
}

.main-content {
    background-color: #6b21a8;
}

.dashboard-container {
    padding: 10px;
}

.dashboard-card {
    background-color: #1e293b;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.dashboard-card-header {
    padding: 15px 20px;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.dashboard-card-body {
    padding: 20px;
}

/* Order Statistics Styles */
.order-statistics-section .dashboard-card-body {
    padding: 20px;
}

.order-stats-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: space-between;
    margin-bottom: 20px;
}

.order-stat-card {
    background: linear-gradient(135deg, #1e293b, #2d3748);
    border-radius: 10px;
    padding: 20px;
    flex: 1;
    min-width: 200px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.order-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.order-stat-icon {
    background-color: rgba(255, 255, 255, 0.1);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.order-stat-icon i {
    font-size: 24px;
}

.order-stat-content {
    flex: 1;
}

.order-stat-value {
    font-size: 24px;
    font-weight: 700;
    margin: 0;
    color: #ffffff;
}

.order-stat-label {
    font-size: 14px;
    color: #a0aec0;
    margin: 5px 0 0 0;
}

@media (max-width: 768px) {
    .order-stats-container {
        flex-direction: column;
    }

    .order-stat-card {
        width: 100%;
    }
}

/* Top Product Section Styles */
.top-product-section {
    margin-top: 20px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 15px;
}

.top-product-card {
    background: linear-gradient(135deg, #1e293b, #2d3748);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.top-product-info {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 15px;
}

.product-name {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 5px 0;
}

.product-details {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 14px;
}

.product-vendor {
    color: #4CAF50;
    font-weight: 500;
}

.product-variant {
    color: #a0aec0;
    font-style: italic;
}

.top-product-stats {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 15px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 100px;
    background-color: rgba(255, 255, 255, 0.05);
    padding: 10px;
    border-radius: 8px;
    text-align: center;
}

.stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #a0aec0;
}

/* Stock level indicators */
.stock-level .stat-value {
    padding: 2px 8px;
    border-radius: 4px;
}

.low-stock {
    background-color: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

.medium-stock {
    background-color: rgba(255, 152, 0, 0.2);
    color: #ff9800;
}

.good-stock {
    background-color: rgba(76, 175, 80, 0.2);
    color: #4caf50;
}

/* Dashboard Tabs Styles */
.dashboard-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 15px;
    padding: 0 20px;
}

.dashboard-tab-btn {
    background: none;
    border: none;
    color: #a0aec0;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.dashboard-tab-btn:hover {
    color: #ffffff;
}

.dashboard-tab-btn.active {
    color: #ffffff;
}

.dashboard-tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, #4CAF50, #2196F3);
    border-radius: 3px 3px 0 0;
}

.dashboard-tab-content {
    display: none;
}

.dashboard-tab-content.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Enterprise Features Styles */
.enterprise-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin-bottom: 15px;
}

.enterprise-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.enterprise-feature-item {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.5), rgba(30, 41, 59, 0.8));
    border-radius: 10px;
    padding: 15px;
    display: flex;
    gap: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.enterprise-feature-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: 18px;
    color: #4CAF50;
}

.feature-content {
    flex: 1;
}

.feature-content h5 {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 8px;
}

.feature-content p {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 10px;
}

.btn-feature-request {
    background-color: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.3);
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-feature-request:hover {
    background-color: rgba(76, 175, 80, 0.3);
    color: #ffffff;
}

/* Premium Entitlements Styles */
.entitlements-title {
    color: #ffffff;
    font-size: 16px;
    margin-bottom: 10px;
}

.premium-item {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 10px;
    margin-top: 10px;
}

.premium-item-icon {
    width: 30px;
    height: 30px;
    background-color: rgba(255, 215, 0, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

.premium-item-icon i {
    font-size: 14px;
    color: #FFD700;
}

.premium-item-content {
    flex: 1;
}

.premium-item-title {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
}

.premium-item-price {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.original-price {
    text-decoration: line-through;
    color: rgba(255, 255, 255, 0.5);
    margin-right: 5px;
}

.current-price {
    color: #4CAF50;
    font-weight: 600;
}

.premium-btn {
    background-color: rgba(255, 215, 0, 0.2);
    color: #FFD700;
    border: 1px solid rgba(255, 215, 0, 0.3);
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.premium-btn:hover {
    background-color: rgba(255, 215, 0, 0.3);
    color: #ffffff;
}

/* Inventory Statistics Styles */
.inventory-stats-header {
    margin-bottom: 20px;
}

.inventory-stats-title {
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 5px;
}

.inventory-stats-subtitle {
    font-size: 14px;
    color: #a0aec0;
    margin-bottom: 0;
}

.inventory-stats-table-container {
    overflow-x: auto;
    background: linear-gradient(135deg, #1e293b, #2d3748);
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.inventory-stats-table {
    width: 100%;
    border-collapse: collapse;
}

.inventory-stats-table th,
.inventory-stats-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.inventory-stats-table th {
    background-color: rgba(0, 0, 0, 0.2);
    color: #ffffff;
    font-weight: 600;
    font-size: 14px;
}

.inventory-stats-table tbody tr {
    transition: background-color 0.3s ease;
}

.inventory-stats-table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.inventory-stats-table tbody td {
    color: #a0aec0;
    font-size: 14px;
}

.inventory-stats-table tbody td:first-child {
    color: #ffffff;
    font-weight: 500;
}

.inventory-stats-table tfoot tr {
    background-color: rgba(76, 175, 80, 0.1);
}

.inventory-stats-table tfoot td {
    color: #4CAF50;
    font-size: 14px;
    border-bottom: none;
}

.inventory-stats-table .no-data {
    text-align: center;
    padding: 30px;
    color: #a0aec0;
    font-style: italic;
}

.totals-row {
    border-top: 2px solid rgba(76, 175, 80, 0.3);
}

@media (max-width: 768px) {
    .inventory-stats-table th,
    .inventory-stats-table td {
        padding: 8px 10px;
        font-size: 12px;
    }

    .inventory-stats-title {
        font-size: 18px;
    }

    .inventory-stats-subtitle {
        font-size: 12px;
    }
}

.no-data-message {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196F3;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    font-size: 14px;
}

@media (max-width: 576px) {
    .top-product-stats {
        flex-direction: column;
    }

    .stat-item {
        width: 100%;
    }
}

/* Quick Access Section - Ultra Modern with Enhanced Colors */
.quick-access-section {
    background: linear-gradient(135deg,
        rgba(20, 20, 40, 0.98) 0%,
        rgba(30, 30, 50, 0.95) 30%,
        rgba(40, 40, 60, 0.92) 70%,
        rgba(50, 50, 70, 0.9) 100%);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 20px;
    overflow: hidden;
    margin-bottom: 24px;
    margin-top: 8px;
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.08) inset,
        0 4px 0 rgba(255, 255, 255, 0.15) inset;
    position: relative;
    backdrop-filter: blur(25px) saturate(200%);
    transform: translateZ(0);
}

.quick-access-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
        #ff6b6b 0%,
        #4ecdc4 15%,
        #45b7d1 30%,
        #96c93d 45%,
        #feca57 60%,
        #ff9ff3 75%,
        #54a0ff 90%,
        #5f27cd 100%);
    z-index: 2;
    opacity: 0.9;
    animation: colorShift 8s ease-in-out infinite;
}

/* Header styles removed as requested */

/* Quick Access Cards */
.quick-access-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 6px;
    padding: 16px 20px 20px 20px;
    transform-origin: top center;
    margin: 0 auto;
    max-width: 100%;
    perspective: 1000px;
}

@media (max-width: 1200px) {
    .quick-access-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
        padding: 16px 18px 18px 18px;
    }
}

@media (max-width: 768px) {
    .quick-access-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
        padding: 14px 16px 16px 16px;
    }

    .quick-access-card {
        min-height: 70px;
        padding: 12px 8px;
    }

    .quick-access-card-icon {
        width: 32px;
        height: 32px;
    }

    .quick-access-card i {
        font-size: 14px;
    }

    .quick-access-card span {
        font-size: 10px;
    }
}

@media (max-width: 480px) {
    .quick-access-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 5px;
        padding: 12px 14px 14px 14px;
    }

    .quick-access-card {
        min-height: 65px;
        padding: 10px 6px;
    }
}

.quick-access-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.04) 50%,
        rgba(255, 255, 255, 0.02) 100%);
    padding: 16px 12px;
    cursor: pointer;
    height: 100%;
    border-radius: 14px;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
    margin: 4px;
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.15),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    text-decoration: none;
    border: 1px solid rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(15px) saturate(150%);
    min-height: 85px;
    transform: translateZ(0);
    will-change: transform, box-shadow, background;
}

.quick-access-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 64, 129, 0.15) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 0;
}

.quick-access-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.1) 0%,
        rgba(118, 75, 162, 0.1) 50%,
        rgba(240, 147, 251, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    border-radius: inherit;
}

.quick-access-card:hover {
    transform: translateY(-6px) scale(1.03);
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.08) 50%,
        rgba(255, 255, 255, 0.04) 100%);
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.25),
        0 8px 20px rgba(102, 126, 234, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.2);
}

.quick-access-card:hover::before {
    opacity: 1;
}

.quick-access-card:active {
    transform: translateY(-1px) scale(1.01);
    transition: transform 0.1s ease;
}

.quick-access-card-icon {
    width: 42px;
    height: 42px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.2) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.25);
    overflow: hidden;
}

.quick-access-card-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%);
    transform: rotate(-45deg) translateX(-100%);
    transition: transform 0.6s ease;
}

.quick-access-card:hover .quick-access-card-icon {
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.2) 0%,
        rgba(118, 75, 162, 0.15) 50%,
        rgba(240, 147, 251, 0.1) 100%);
    transform: translateY(-2px) rotate(2deg);
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.15),
        0 2px 8px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.quick-access-card:hover .quick-access-card-icon::before {
    transform: rotate(-45deg) translateX(100%);
}

.quick-access-card i {
    font-size: 16px;
    color: #ffffff;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    z-index: 3;
    opacity: 0.85;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.quick-access-card:hover i {
    opacity: 1;
    color: #ffffff;
    transform: scale(1.1) translateY(-1px);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4));
    text-shadow:
        0 0 15px rgba(255, 255, 255, 0.6),
        0 0 25px rgba(102, 126, 234, 0.4);
}

/* Specific Card Color Themes */

/* POS Card - Green Theme */
.quick-access-card.pos:hover {
    background: linear-gradient(145deg,
        rgba(76, 175, 80, 0.15) 0%,
        rgba(76, 175, 80, 0.08) 50%,
        rgba(76, 175, 80, 0.04) 100%);
    box-shadow:
        0 15px 35px rgba(76, 175, 80, 0.2),
        0 8px 20px rgba(76, 175, 80, 0.15),
        inset 0 2px 0 rgba(76, 175, 80, 0.3);
}

.quick-access-card.pos:hover .quick-access-card-icon {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.15)) !important;
    border-color: rgba(76, 175, 80, 0.4);
    box-shadow:
        0 4px 15px rgba(76, 175, 80, 0.2),
        0 2px 8px rgba(76, 175, 80, 0.3),
        inset 0 1px 0 rgba(76, 175, 80, 0.4);
}

.quick-access-card.pos:hover i {
    color: #4CAF50 !important;
    text-shadow: 0 0 15px rgba(76, 175, 80, 0.6);
}

/* Inventory Card - Blue Theme */
.quick-access-card.inventory:hover {
    background: linear-gradient(145deg,
        rgba(33, 150, 243, 0.15) 0%,
        rgba(33, 150, 243, 0.08) 50%,
        rgba(33, 150, 243, 0.04) 100%);
    box-shadow:
        0 15px 35px rgba(33, 150, 243, 0.2),
        0 8px 20px rgba(33, 150, 243, 0.15),
        inset 0 2px 0 rgba(33, 150, 243, 0.3);
}

.quick-access-card.inventory:hover .quick-access-card-icon {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.3), rgba(33, 150, 243, 0.15)) !important;
    border-color: rgba(33, 150, 243, 0.4);
    box-shadow:
        0 4px 15px rgba(33, 150, 243, 0.2),
        0 2px 8px rgba(33, 150, 243, 0.3),
        inset 0 1px 0 rgba(33, 150, 243, 0.4);
}

.quick-access-card.inventory:hover i {
    color: #2196F3 !important;
    text-shadow: 0 0 15px rgba(33, 150, 243, 0.6);
}

/* Customers Card - Purple Theme */
.quick-access-card.customers:hover {
    background: linear-gradient(145deg,
        rgba(156, 39, 176, 0.15) 0%,
        rgba(156, 39, 176, 0.08) 50%,
        rgba(156, 39, 176, 0.04) 100%);
    box-shadow:
        0 15px 35px rgba(156, 39, 176, 0.2),
        0 8px 20px rgba(156, 39, 176, 0.15),
        inset 0 2px 0 rgba(156, 39, 176, 0.3);
}

.quick-access-card.customers:hover .quick-access-card-icon {
    background: linear-gradient(135deg, rgba(156, 39, 176, 0.3), rgba(156, 39, 176, 0.15)) !important;
    border-color: rgba(156, 39, 176, 0.4);
    box-shadow:
        0 4px 15px rgba(156, 39, 176, 0.2),
        0 2px 8px rgba(156, 39, 176, 0.3),
        inset 0 1px 0 rgba(156, 39, 176, 0.4);
}

.quick-access-card.customers:hover i {
    color: #9C27B0 !important;
    text-shadow: 0 0 15px rgba(156, 39, 176, 0.6);
}

/* Buylist Card - Orange Theme */
.quick-access-card.buylist:hover {
    background: linear-gradient(145deg,
        rgba(255, 152, 0, 0.15) 0%,
        rgba(255, 152, 0, 0.08) 50%,
        rgba(255, 152, 0, 0.04) 100%);
    box-shadow:
        0 15px 35px rgba(255, 152, 0, 0.2),
        0 8px 20px rgba(255, 152, 0, 0.15),
        inset 0 2px 0 rgba(255, 152, 0, 0.3);
}

.quick-access-card.buylist:hover .quick-access-card-icon {
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.3), rgba(255, 152, 0, 0.15)) !important;
    border-color: rgba(255, 152, 0, 0.4);
    box-shadow:
        0 4px 15px rgba(255, 152, 0, 0.2),
        0 2px 8px rgba(255, 152, 0, 0.3),
        inset 0 1px 0 rgba(255, 152, 0, 0.4);
}

.quick-access-card.buylist:hover i {
    color: #FF9800 !important;
    text-shadow: 0 0 15px rgba(255, 152, 0, 0.6);
}

/* Admin Card - Red Theme */
.quick-access-card.admin:hover {
    background: linear-gradient(145deg,
        rgba(244, 67, 54, 0.15) 0%,
        rgba(244, 67, 54, 0.08) 50%,
        rgba(244, 67, 54, 0.04) 100%);
    box-shadow:
        0 15px 35px rgba(244, 67, 54, 0.2),
        0 8px 20px rgba(244, 67, 54, 0.15),
        inset 0 2px 0 rgba(244, 67, 54, 0.3);
}

.quick-access-card.admin:hover .quick-access-card-icon {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.3), rgba(244, 67, 54, 0.15)) !important;
    border-color: rgba(244, 67, 54, 0.4);
    box-shadow:
        0 4px 15px rgba(244, 67, 54, 0.2),
        0 2px 8px rgba(244, 67, 54, 0.3),
        inset 0 1px 0 rgba(244, 67, 54, 0.4);
}

.quick-access-card.admin:hover i {
    color: #F44336 !important;
    text-shadow: 0 0 15px rgba(244, 67, 54, 0.6);
}

.quick-access-card-content {
    position: relative;
    z-index: 1;
}

.quick-access-card span {
    background: linear-gradient(135deg,
        #ffffff 0%,
        rgba(255, 255, 255, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 12px;
    font-weight: 700;
    margin-bottom: 2px;
    letter-spacing: 0.8px;
    position: relative;
    z-index: 2;
    opacity: 0.95;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    text-transform: uppercase;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.quick-access-card:hover span {
    background: linear-gradient(135deg,
        #ffffff 0%,
        #f8f9fa 30%,
        #e3f2fd 60%,
        #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    opacity: 1;
    transform: translateY(-2px) scale(1.05);
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
    letter-spacing: 1px;
}

/* Ultra-Modern Animations & Effects */
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-1px); }
}

@keyframes colorShift {
    0% {
        background: linear-gradient(90deg,
            #ff6b6b 0%,
            #4ecdc4 15%,
            #45b7d1 30%,
            #96c93d 45%,
            #feca57 60%,
            #ff9ff3 75%,
            #54a0ff 90%,
            #5f27cd 100%);
    }
    25% {
        background: linear-gradient(90deg,
            #5f27cd 0%,
            #ff6b6b 15%,
            #4ecdc4 30%,
            #45b7d1 45%,
            #96c93d 60%,
            #feca57 75%,
            #ff9ff3 90%,
            #54a0ff 100%);
    }
    50% {
        background: linear-gradient(90deg,
            #54a0ff 0%,
            #5f27cd 15%,
            #ff6b6b 30%,
            #4ecdc4 45%,
            #45b7d1 60%,
            #96c93d 75%,
            #feca57 90%,
            #ff9ff3 100%);
    }
    75% {
        background: linear-gradient(90deg,
            #ff9ff3 0%,
            #54a0ff 15%,
            #5f27cd 30%,
            #ff6b6b 45%,
            #4ecdc4 60%,
            #45b7d1 75%,
            #96c93d 90%,
            #feca57 100%);
    }
    100% {
        background: linear-gradient(90deg,
            #ff6b6b 0%,
            #4ecdc4 15%,
            #45b7d1 30%,
            #96c93d 45%,
            #feca57 60%,
            #ff9ff3 75%,
            #54a0ff 90%,
            #5f27cd 100%);
    }
}

.quick-access-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.03),
        transparent);
    animation: shimmer 4s infinite;
    pointer-events: none;
    z-index: 1;
}

.quick-access-card {
    animation: float 8s ease-in-out infinite;
}

.quick-access-card:nth-child(1) { animation-delay: 0s; }
.quick-access-card:nth-child(2) { animation-delay: 0.8s; }
.quick-access-card:nth-child(3) { animation-delay: 1.6s; }
.quick-access-card:nth-child(4) { animation-delay: 2.4s; }
.quick-access-card:nth-child(5) { animation-delay: 3.2s; }

/* Performance optimizations */
.quick-access-card,
.quick-access-card-icon {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Accessibility: Disable animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
    .quick-access-card,
    .quick-access-section::after {
        animation: none;
    }

    .quick-access-card,
    .quick-access-card-icon,
    .quick-access-card i,
    .quick-access-card span {
        transition: none;
    }
}

.quick-access-card small {
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px;
    font-weight: 400;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.quick-access-card:hover .quick-access-card-content {
    transform: translateY(2px);
}

/* Removed subtitle styling */

/* Enterprise Features Section Styles */
.enterprise-highlight-section {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 16px;
    overflow: hidden;
    margin-bottom: 15px; /* Reduced margin */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    position: relative;
}

.enterprise-highlight-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, #FFD700, #ff4081, #7e57c2);
    z-index: 2;
}

.bg-gradient-premium {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 20px;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 1px;
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.enterprise-title {
    color: #ffffff;
    font-size: 20px; /* Reduced font size */
    font-weight: 700;
    margin-bottom: 5px; /* Reduced margin */
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.enterprise-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px; /* Reduced font size */
    margin-bottom: 15px; /* Reduced margin */
}

.enterprise-features {
    display: flex;
    flex-direction: column;
    gap: 10px; /* Reduced gap */
}

.enterprise-feature-item {
    display: flex;
    gap: 10px; /* Reduced gap */
    background-color: rgba(255, 255, 255, 0.05);
    padding: 12px; /* Reduced padding */
    border-radius: 10px; /* Smaller radius */
    transition: all 0.3s ease;
}

.enterprise-feature-item:hover {
    background-color: rgba(255, 255, 255, 0.08);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon i {
    color: #FFD700;
    font-size: 18px;
}

.feature-content h5 {
    color: #ffffff;
    font-size: 16px; /* Reduced font size */
    font-weight: 600;
    margin-bottom: 4px; /* Reduced margin */
}

.feature-content p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px; /* Reduced font size */
    line-height: 1.4; /* Reduced line height */
    margin-bottom: 0;
}

.btn-feature-request {
    background: linear-gradient(135deg, #6b21a8, #4c1d95);
    color: white;
    border: none;
    padding: 5px 10px; /* Reduced padding */
    border-radius: 4px; /* Smaller radius */
    font-size: 12px; /* Reduced font size */
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 3px 8px rgba(107, 33, 168, 0.3); /* Smaller shadow */
    align-self: flex-start;
    margin-top: 5px !important; /* Reduced margin */
}

.btn-feature-request:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(107, 33, 168, 0.4);
    color: white;
    background: linear-gradient(135deg, #7c3adb, #5a24b5);
}

.dedicated-support-card {
    background: linear-gradient(135deg, #2d3748, #1a202c);
    border-radius: 12px; /* Smaller radius */
    padding: 15px; /* Reduced padding */
    height: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2); /* Smaller shadow */
    position: relative;
    overflow: hidden;
}

.dedicated-support-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.support-header {
    display: flex;
    align-items: center;
    gap: 8px; /* Reduced gap */
    margin-bottom: 10px; /* Reduced margin */
    padding-bottom: 8px; /* Reduced padding */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.support-header i {
    font-size: 24px;
    color: #FFD700;
}

.support-header h5 {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.support-profile {
    display: flex;
    align-items: center;
    gap: 10px; /* Reduced gap */
    margin-bottom: 10px; /* Reduced margin */
}

.support-avatar {
    width: 45px; /* Reduced size */
    height: 45px; /* Reduced size */
    background: linear-gradient(135deg, #6b21a8, #4c1d95);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 8px rgba(107, 33, 168, 0.3); /* Smaller shadow */
}

.support-avatar i {
    font-size: 24px;
    color: #ffffff;
}

.support-info h6 {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.support-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin: 0;
}

.support-details {
    display: flex;
    flex-direction: column;
    gap: 6px; /* Reduced gap */
    margin-bottom: 12px; /* Reduced margin */
}

.support-detail-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.support-detail-item i {
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #FFD700;
}

.support-detail-item span {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
}

.btn-contact-support {
    background: linear-gradient(135deg, #6b21a8, #4c1d95);
    color: white;
    border: none;
    padding: 8px; /* Reduced padding */
    border-radius: 6px; /* Smaller radius */
    font-weight: 600;
    font-size: 13px; /* Reduced font size */
    text-align: center;
    transition: all 0.3s ease;
    margin-top: auto;
    box-shadow: 0 3px 8px rgba(107, 33, 168, 0.3); /* Smaller shadow */
    text-decoration: none;
}

.btn-contact-support:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(107, 33, 168, 0.4);
    color: white;
}

/* Entitlements section styling */
.entitlements-section {
    margin: 10px 0; /* Reduced margin */
    padding: 10px; /* Reduced padding */
    background: rgba(255, 215, 0, 0.05);
    border-radius: 8px; /* Smaller radius */
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.entitlements-title {
    color: #FFD700;
    font-size: 14px; /* Reduced font size */
    font-weight: 600;
    margin-bottom: 8px; /* Reduced margin */
    text-align: center;
    padding-bottom: 5px; /* Reduced padding */
    border-bottom: 1px solid rgba(255, 215, 0, 0.1);
}

.entitlement-item {
    display: flex;
    align-items: center;
    gap: 6px; /* Reduced gap */
    margin-bottom: 4px; /* Reduced margin */
    padding: 3px 0; /* Reduced padding */
}

.entitlement-item i {
    color: #2ecc71;
    font-size: 14px;
}

.entitlement-item span {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
}

@media (max-width: 768px) {
    .enterprise-features {
        gap: 15px;
    }

    .enterprise-feature-item {
        padding: 15px;
    }

    .dedicated-support-card {
        margin-top: 20px;
    }
}

/* Enterprise Modal Styles */
.enterprise-modal {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    color: #ffffff;
    border: none;
    border-radius: 16px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.enterprise-modal .modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 25px;
}

.enterprise-modal .modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
}

.enterprise-modal .modal-title i {
    color: #FFD700;
    margin-right: 10px;
}

.enterprise-modal .modal-body {
    padding: 25px;
}

.enterprise-modal .modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px 25px;
}

.enterprise-modal .form-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 8px;
}

.enterprise-modal .form-control,
.enterprise-modal .form-select {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #ffffff;
    padding: 12px 15px;
    border-radius: 8px;
}

/* Fix dropdown styling for dark mode */
.enterprise-modal .form-select option {
    background-color: #1a1a2e;
    color: #ffffff;
}

/* For Firefox */
.enterprise-modal .form-select:focus {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.08);
}

/* For Chrome/Safari/Edge */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .enterprise-modal .form-select {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    }
}

/* Global styles for select dropdowns when enterprise modal is open */
body.enterprise-dropdown-open select option,
body.enterprise-dropdown-open .dropdown-menu,
body.enterprise-dropdown-open .dropdown-item {
    background-color: #1a1a2e !important;
    color: #ffffff !important;
}

/* For webkit browsers */
body.enterprise-dropdown-open select::-webkit-scrollbar {
    width: 8px;
}

body.enterprise-dropdown-open select::-webkit-scrollbar-track {
    background: #16213e;
}

body.enterprise-dropdown-open select::-webkit-scrollbar-thumb {
    background-color: #6b21a8;
    border-radius: 20px;
}

.enterprise-modal .form-control:focus,
.enterprise-modal .form-select:focus {
    background-color: rgba(255, 255, 255, 0.08);
    border-color: rgba(139, 92, 246, 0.5);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.25);
}

.enterprise-modal .form-control::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

.enterprise-modal .alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    color: rgba(255, 255, 255, 0.9);
}

.enterprise-modal .btn-primary {
    background: linear-gradient(135deg, #6b21a8, #4c1d95);
    border: none;
    padding: 10px 20px;
    font-weight: 500;
    box-shadow: 0 4px 10px rgba(107, 33, 168, 0.3);
}

.enterprise-modal .btn-primary:hover {
    background: linear-gradient(135deg, #7c3adb, #5a24b5);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(107, 33, 168, 0.4);
}

.enterprise-modal .btn-secondary {
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    color: rgba(255, 255, 255, 0.8);
}

.enterprise-modal .btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

/* Success Modal Styles */
.success-modal {
    background: linear-gradient(135deg, #064e3b, #065f46);
    color: #ffffff;
    border: none;
    border-radius: 16px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
    text-align: center;
}

.success-modal .modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 25px;
}

.success-modal .modal-title i {
    color: #10b981;
}

.success-modal .success-icon-container {
    width: 80px;
    height: 80px;
    background: rgba(16, 185, 129, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.success-modal .success-icon-container i {
    font-size: 36px;
    color: #10b981;
}

.success-modal .btn-success {
    background: linear-gradient(135deg, #047857, #065f46);
    border: none;
    padding: 10px 30px;
    font-weight: 500;
    box-shadow: 0 4px 10px rgba(5, 150, 105, 0.3);
}

.success-modal .btn-success:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(5, 150, 105, 0.4);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    background: #2d3748;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    max-width: 400px;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    overflow: hidden;
}

.toast-visible {
    transform: translateX(0);
}

.toast-hiding {
    transform: translateX(120%);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.toast-content i {
    font-size: 20px;
}

.toast-info i {
    color: #3b82f6;
}

.toast-success i {
    color: #10b981;
}

.toast-error i {
    color: #ef4444;
}

.toast-close {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    padding: 0;
    font-size: 14px;
    transition: color 0.2s ease;
}

.toast-close:hover {
    color: white;
}

/* Ripple Effect */
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    animation: ripple 0.6s linear;
    transform: scale(0);
    pointer-events: none;
    z-index: 0;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Premium button styles */
.premium-btn {
    background: linear-gradient(135deg, #6b21a8, #4c1d95);
    color: white;
    border: none;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 3px 8px rgba(107, 33, 168, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    width: fit-content;
    min-width: 120px;
}

.premium-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(107, 33, 168, 0.4);
    background: linear-gradient(135deg, #7c3adb, #5a24b5);
}

.premium-btn i {
    font-size: 10px;
}

.order-free-design-btn {
    margin: 15px auto 10px;
}

.order-premium-btn {
    width: 120px;
    margin-left: auto;
}

/* Premium item styles */
.premium-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 10px 12px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.premium-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.premium-item-icon {
    width: 28px;
    height: 28px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

.premium-item-icon i {
    color: #FFD700;
    font-size: 14px;
}

.premium-item-content {
    flex: 1;
}

.premium-item-title {
    font-weight: 600;
    color: #fff;
    margin-bottom: 2px;
    font-size: 14px;
}

.premium-item-price {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.premium-item-price .original-price {
    text-decoration: line-through;
    margin-right: 5px;
    opacity: 0.7;
}

.premium-item-price .current-price {
    color: #FFD700;
    font-weight: 600;
}
