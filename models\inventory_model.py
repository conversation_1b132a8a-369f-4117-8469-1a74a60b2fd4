from datetime import datetime
from mongoengine import Document, StringField, IntField, BooleanField, DateTimeField, DictField, FloatField

class Inventory(Document):
    userId = StringField(required=True)
    username = StringField(required=True)
    catalogId = StringField(required=True)
    price = FloatField()
    skuId = StringField(required=True)
    productId = StringField(required=True)
    name = StringField(required=True)
    quantity = IntField(required=True, min_value=0)
    condition = StringField(required=True)
    gameName = StringField(required=True)
    expansionName = StringField(required=True)
    number = StringField()
    tcgplayerId = StringField()
    imageUrl = StringField()
    language = StringField(default="English")
    printType = StringField()
    location = StringField()
    notes = StringField()
    lastModified = DateTimeField(default=datetime.utcnow)
    createdAt = DateTimeField(default=datetime.utcnow)
    customFields = DictField()
    
    # Shopify sync fields
    last_sync_error = StringField()
    last_sync_attempt = DateTimeField()
    needs_shopify_sync = BooleanField(default=False)
    shopify_sync_timestamp = DateTimeField()
    last_processed = DateTimeField()
    last_sync_status = StringField()

    meta = {
        'collection': 'inventory',
        'indexes': [
            'username',
            'catalogId',
            'skuId',
            'gameName',
            'expansionName',
            'name',
            'location'
        ]
    }
