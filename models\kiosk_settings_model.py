from mongoengine import (
    Document, <PERSON><PERSON>ield, BooleanField, <PERSON>ctField, 
    <PERSON>loatField, DateTimeField, IntField
)
from datetime import datetime

class KioskSettings(Document):
    username = StringField(required=True, unique=True)
    store_name = StringField()
    shopify_store_name = StringField()
    shopify_access_token = StringField()
    shopify_location_id = StringField()
    currency = StringField(default='GBP')
    tax_included = BooleanField(default=False)
    order_prefix = StringField()
    receipt_footer = StringField()
    auto_print = BooleanField(default=False)
    inactivity_timeout = IntField(default=30)
    payment_methods = DictField(default={
        'cash': True,
        'card': True,
        'store_credit': True
    })
    min_store_credit = FloatField(default=0.0)
    email_notifications = BooleanField(default=False)
    notification_email = StringField()
    notify_events = DictField(default={
        'new_order': True,
        'low_stock': True,
        'returns': True
    })
    
    # Display settings for main kiosk screen
    display_settings = Dict<PERSON>ield(default={
        'show_place_order': True,
        'show_sell_to_us': True,
        'show_events': True,
        'show_check_in': True
    })
    screensaver_image = StringField()  # Path to the screensaver image
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'kioskSettings',
        'indexes': [
            'username'
        ]
    }

    def save(self, *args, **kwargs):
        self.updated_at = datetime.utcnow()
        return super(KioskSettings, self).save(*args, **kwargs)

    @classmethod
    def get_settings(cls, username):
        try:
            return cls.objects(username=username).first()
        except Exception:
            return None

    @classmethod
    def save_settings(cls, settings_data):
        username = settings_data.pop('username')
        settings = cls.objects(username=username).modify(
            upsert=True,
            new=True,
            set__username=username,
            set__store_name=settings_data.get('storeName'),
            set__shopify_store_name=settings_data.get('shopifyStoreName'),
            set__shopify_access_token=settings_data.get('shopifyAccessToken'),
            set__currency=settings_data.get('currency', 'GBP'),
            set__tax_included=bool(settings_data.get('taxIncluded')),
            set__order_prefix=settings_data.get('orderPrefix'),
            set__receipt_footer=settings_data.get('receiptFooter'),
            set__auto_print=bool(settings_data.get('autoPrint')),
            set__inactivity_timeout=int(settings_data.get('inactivityTimeout', 30)),
            set__min_store_credit=float(settings_data.get('minStoreCredit', 0.0) or 0.0),
            set__email_notifications=bool(settings_data.get('emailNotifications')),
            set__notification_email=settings_data.get('notificationEmail'),
            set__payment_methods={
                'cash': 'cash' in settings_data.get('paymentMethods', []),
                'card': 'card' in settings_data.get('paymentMethods', []),
                'store_credit': 'storeCredit' in settings_data.get('paymentMethods', [])
            },
            set__notify_events={
                'new_order': 'newOrder' in settings_data.get('notifyEvents', []),
                'low_stock': 'lowStock' in settings_data.get('notifyEvents', []),
                'returns': 'returns' in settings_data.get('notifyEvents', [])
            } if 'notifyEvents' in settings_data else settings_data.get('notify_events', {}),
            set__display_settings={
                'show_place_order': 'showPlaceOrder' in settings_data.get('displaySettings', []),
                'show_sell_to_us': 'showSellToUs' in settings_data.get('displaySettings', []),
                'show_events': 'showEvents' in settings_data.get('displaySettings', []),
                'show_check_in': 'showCheckIn' in settings_data.get('displaySettings', [])
            } if 'displaySettings' in settings_data else settings_data.get('display_settings', {}),
            set__screensaver_image=settings_data.get('screensaverImage')
        )
        return settings
