from mongoengine import Document, StringField, IntField, FloatField, ListField, BooleanField, DictField

class Other(Document):
    id = StringField()
    name = StringField(required=True)
    url = StringField()
    availability = StringField()
    boxBreak = StringField()
    brand = StringField()
    caseCount = StringField()
    categories = ListField(DictField())
    configuration = StringField()
    createdAt = StringField()
    description = StringField()
    descriptionText = StringField()
    downloadedImages = StringField()
    images = ListField(StringField())
    metaTags = DictField()
    originalData = DictField()
    originalUrl = StringField()
    price = StringField()
    priceText = StringField()
    productType = StringField()
    relatedProducts = StringField()
    releaseYear = StringField()
    scrapedAt = StringField()
    sku = StringField()
    soldAs = StringField()
    specifications = DictField()
    sportType = StringField()
    structuredData = StringField()
    updatedAt = StringField()
    variants = StringField()

    meta = {
        'db_alias': 'test',
        'collection': 'sportsSealed',  # Use the correct collection name
        'indexes': [
            'name',
            'brand',
            'releaseYear',
            'productType',
            'sportType',
            'sku',
            'categories.name'  # Add index on categories.name for faster category filtering
        ]
    }
