from mongoengine import Document, EmbeddedDocument, StringField, FloatField, DateTimeField, BooleanField, IntField, ListField, DictField, ReferenceField, EmbeddedDocumentField
from datetime import datetime

class PricingRule(Document):
    """Model for storing pricing rules and automation settings"""
    
    # Rule identification
    name = StringField(required=True, max_length=100)
    description = StringField(max_length=500)
    
    # Rule type and configuration
    rule_type = StringField(required=True, choices=[
        'markup_percentage',  # Apply percentage markup
        'fixed_margin',       # Apply fixed margin amount
        'competitive_pricing', # Price based on competitor data
        'bulk_discount',      # Volume-based pricing
        'category_pricing',   # Category-specific rules
        'vendor_pricing'      # Vendor-specific rules
    ])
    
    # Rule parameters (flexible structure for different rule types)
    parameters = DictField()  # Store rule-specific parameters
    
    # Filters for when this rule applies
    filters = DictField()  # Store conditions like vendor, category, price range, etc.
    
    # Rule status and metadata
    is_active = BooleanField(default=True)
    priority = IntField(default=0)  # Higher number = higher priority
    
    # Audit fields
    created_by = StringField(required=True)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_by = StringField()
    updated_at = DateTimeField()
    
    # Usage statistics
    times_applied = IntField(default=0)
    last_applied = DateTimeField()
    
    meta = {
        'collection': 'pricing_rules',
        'indexes': [
            'rule_type',
            'is_active',
            'priority',
            'created_at'
        ]
    }
    
    def __str__(self):
        return f"PricingRule: {self.name} ({self.rule_type})"

class PricingHistory(Document):
    """Model for tracking all pricing changes"""
    
    # Product reference
    product_id = StringField(required=True)  # Reference to product
    product_name = StringField()  # Cached for easier querying
    vendor = StringField()  # Cached vendor info
    
    # Price change details
    old_price = FloatField(required=True)
    new_price = FloatField(required=True)
    price_change = FloatField()  # Calculated: new_price - old_price
    price_change_percentage = FloatField()  # Calculated percentage change
    
    # Change metadata
    change_reason = StringField(choices=[
        'manual_update',
        'bulk_update', 
        'pricing_rule',
        'competitive_adjustment',
        'cost_change',
        'promotion',
        'market_adjustment',
        'inventory_adjustment'
    ])
    
    change_description = StringField(max_length=500)
    
    # Applied rule (if applicable)
    applied_rule_id = StringField()  # Reference to PricingRule if rule-based
    applied_rule_name = StringField()
    
    # Audit fields
    updated_by = StringField(required=True)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    # Additional context
    old_markup_percentage = FloatField()
    new_markup_percentage = FloatField()
    cost_at_time = FloatField()  # Product cost when price was changed
    
    meta = {
        'collection': 'pricing_history',
        'ordering': ['-updated_at'],
        'indexes': [
            'product_id',
            'updated_at',
            'updated_by',
            'change_reason',
            'applied_rule_id'
        ]
    }
    
    def save(self, *args, **kwargs):
        # Calculate derived fields before saving
        if self.old_price and self.new_price:
            self.price_change = self.new_price - self.old_price
            if self.old_price > 0:
                self.price_change_percentage = (self.price_change / self.old_price) * 100
        
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"PricingHistory: {self.product_name} - £{self.old_price} → £{self.new_price}"

class CompetitorPrice(EmbeddedDocument):
    """Embedded document for storing competitor pricing data"""
    
    competitor_name = StringField(required=True)
    price = FloatField(required=True)
    currency = StringField(default='GBP')
    condition = StringField()  # NM, LP, MP, etc.
    in_stock = BooleanField(default=True)
    last_updated = DateTimeField(default=datetime.utcnow)
    source_url = StringField()

class PricingAnalytics(Document):
    """Model for storing pricing analytics and insights"""
    
    # Product reference
    product_id = StringField(required=True)
    product_name = StringField()
    vendor = StringField()
    category = StringField()
    
    # Current pricing data
    current_price = FloatField()
    current_cost = FloatField()
    current_markup_percentage = FloatField()
    
    # Market data
    competitor_prices = ListField(EmbeddedDocumentField(CompetitorPrice))
    market_average_price = FloatField()
    market_min_price = FloatField()
    market_max_price = FloatField()
    
    # Performance metrics
    sales_velocity = FloatField()  # Units sold per day
    profit_margin = FloatField()
    revenue_last_30_days = FloatField()
    units_sold_last_30_days = IntField()
    
    # Pricing recommendations
    recommended_price = FloatField()
    recommendation_reason = StringField()
    confidence_score = FloatField()  # 0-100 confidence in recommendation
    
    # Timestamps
    last_analyzed = DateTimeField(default=datetime.utcnow)
    last_competitor_check = DateTimeField()
    
    meta = {
        'collection': 'pricing_analytics',
        'indexes': [
            'product_id',
            'vendor',
            'category',
            'last_analyzed',
            'sales_velocity',
            'profit_margin'
        ]
    }
    
    def __str__(self):
        return f"PricingAnalytics: {self.product_name} - £{self.current_price}"

class BulkPricingJob(Document):
    """Model for tracking bulk pricing operations"""
    
    # Job identification
    job_id = StringField(required=True, unique=True)
    job_name = StringField(required=True)
    
    # Job configuration
    update_type = StringField(required=True, choices=[
        'percentage_increase',
        'percentage_decrease', 
        'fixed_amount_increase',
        'fixed_amount_decrease',
        'set_price',
        'apply_rule'
    ])
    
    update_value = FloatField()  # The value to apply (percentage, amount, etc.)
    filters = DictField()  # Filters for which products to update
    
    # Job status
    status = StringField(default='pending', choices=[
        'pending',
        'running',
        'completed',
        'failed',
        'cancelled'
    ])
    
    # Progress tracking
    total_products = IntField(default=0)
    processed_products = IntField(default=0)
    successful_updates = IntField(default=0)
    failed_updates = IntField(default=0)
    
    # Results
    error_messages = ListField(StringField())
    preview_mode = BooleanField(default=False)  # If true, don't actually update prices
    
    # Audit fields
    created_by = StringField(required=True)
    created_at = DateTimeField(default=datetime.utcnow)
    started_at = DateTimeField()
    completed_at = DateTimeField()
    
    meta = {
        'collection': 'bulk_pricing_jobs',
        'ordering': ['-created_at'],
        'indexes': [
            'job_id',
            'status',
            'created_by',
            'created_at'
        ]
    }
    
    def __str__(self):
        return f"BulkPricingJob: {self.job_name} ({self.status})"
