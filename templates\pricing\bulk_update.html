{% extends "base.html" %}

{% block title %}Bulk Price Update{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit me-2" style="color: #009688;"></i>
                Bulk Price Update
            </h1>
            <p class="text-muted mb-0">Update multiple product prices at once</p>
        </div>
        <div>
            <a href="{{ url_for('pricing.pricing_dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Bulk Update Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Price Update Configuration</h6>
                </div>
                <div class="card-body">
                    <form id="bulkUpdateForm">
                        <!-- Update Type -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="updateType" class="form-label">Update Type</label>
                                <select class="form-select" id="updateType" name="update_type" required>
                                    <option value="">Select update type...</option>
                                    <option value="percentage">Percentage Change</option>
                                    <option value="fixed_amount">Fixed Amount Change</option>
                                    <option value="set_price">Set Fixed Price</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="updateValue" class="form-label">Value</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="valuePrefix">£</span>
                                    <input type="number" class="form-control" id="updateValue" name="value" step="0.01" required>
                                    <span class="input-group-text" id="valueSuffix" style="display: none;">%</span>
                                </div>
                                <div class="form-text" id="valueHelp">Enter the value for the price update</div>
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="mb-4">
                            <h6 class="text-primary mb-3">Filters (Optional)</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="vendorFilter" class="form-label">Vendor</label>
                                    <input type="text" class="form-control" id="vendorFilter" name="vendor" placeholder="Filter by vendor...">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="productTypeFilter" class="form-label">Product Type</label>
                                    <input type="text" class="form-control" id="productTypeFilter" name="product_type" placeholder="Filter by product type...">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="minPrice" class="form-label">Minimum Price</label>
                                    <div class="input-group">
                                        <span class="input-group-text">£</span>
                                        <input type="number" class="form-control" id="minPrice" name="min_price" step="0.01" placeholder="0.00">
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="maxPrice" class="form-label">Maximum Price</label>
                                    <div class="input-group">
                                        <span class="input-group-text">£</span>
                                        <input type="number" class="form-control" id="maxPrice" name="max_price" step="0.01" placeholder="999.99">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Reason -->
                        <div class="mb-4">
                            <label for="reason" class="form-label">Reason for Update</label>
                            <textarea class="form-control" id="reason" name="reason" rows="3" placeholder="Enter reason for this bulk price update..."></textarea>
                        </div>

                        <!-- Preview Mode -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="previewMode" name="preview_mode" checked>
                                <label class="form-check-label" for="previewMode">
                                    Preview mode (don't actually update prices)
                                </label>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-play me-1"></i>
                                <span id="submitText">Preview Update</span>
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo me-1"></i>
                                Reset
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Help Panel -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Help & Tips</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-success">Update Types:</h6>
                        <ul class="list-unstyled">
                            <li><strong>Percentage Change:</strong> Increase/decrease by %</li>
                            <li><strong>Fixed Amount:</strong> Add/subtract fixed amount</li>
                            <li><strong>Set Fixed Price:</strong> Set all to same price</li>
                        </ul>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-info">Examples:</h6>
                        <ul class="list-unstyled">
                            <li>• +10% increase: Type "10" for percentage</li>
                            <li>• -5% decrease: Type "-5" for percentage</li>
                            <li>• +£2 increase: Type "2" for fixed amount</li>
                            <li>• Set all to £15: Type "15" for set price</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Important:</strong> Always use preview mode first to check which products will be affected!
                    </div>
                </div>
            </div>

            <!-- Progress Panel (hidden initially) -->
            <div class="card shadow mb-4" id="progressPanel" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Update Progress</h6>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar"></div>
                    </div>
                    <div id="progressText">Preparing update...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Panel (hidden initially) -->
    <div class="row" id="resultsPanel" style="display: none;">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Update Results</h6>
                </div>
                <div class="card-body">
                    <div id="resultsContent"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Update form behavior based on update type
document.getElementById('updateType').addEventListener('change', function() {
    const updateType = this.value;
    const valuePrefix = document.getElementById('valuePrefix');
    const valueSuffix = document.getElementById('valueSuffix');
    const valueHelp = document.getElementById('valueHelp');
    const previewCheckbox = document.getElementById('previewMode');
    const submitText = document.getElementById('submitText');

    if (updateType === 'percentage') {
        valuePrefix.style.display = 'none';
        valueSuffix.style.display = 'block';
        valueHelp.textContent = 'Enter percentage change (positive for increase, negative for decrease)';
    } else {
        valuePrefix.style.display = 'block';
        valueSuffix.style.display = 'none';
        if (updateType === 'fixed_amount') {
            valueHelp.textContent = 'Enter amount to add/subtract (positive to add, negative to subtract)';
        } else if (updateType === 'set_price') {
            valueHelp.textContent = 'Enter the fixed price to set for all matching products';
        }
    }

    // Update submit button text
    updateSubmitText();
});

// Update submit button text based on preview mode
document.getElementById('previewMode').addEventListener('change', updateSubmitText);

function updateSubmitText() {
    const previewMode = document.getElementById('previewMode').checked;
    const submitText = document.getElementById('submitText');
    submitText.textContent = previewMode ? 'Preview Update' : 'Apply Update';
}

// Handle form submission
document.getElementById('bulkUpdateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {
        update_type: formData.get('update_type'),
        value: parseFloat(formData.get('value')),
        filters: {
            vendor: formData.get('vendor') || null,
            product_type: formData.get('product_type') || null,
            min_price: formData.get('min_price') ? parseFloat(formData.get('min_price')) : null,
            max_price: formData.get('max_price') ? parseFloat(formData.get('max_price')) : null
        },
        reason: formData.get('reason') || 'Bulk update',
        preview_mode: formData.get('preview_mode') === 'on'
    };

    // Show progress panel
    document.getElementById('progressPanel').style.display = 'block';
    document.getElementById('resultsPanel').style.display = 'none';
    
    // Disable form
    document.getElementById('submitBtn').disabled = true;
    
    // Make API call
    fetch('/pricing/api/bulk-update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        // Hide progress panel
        document.getElementById('progressPanel').style.display = 'none';
        
        // Show results
        document.getElementById('resultsPanel').style.display = 'block';
        const resultsContent = document.getElementById('resultsContent');
        
        if (result.success) {
            resultsContent.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Success!</strong> ${result.message}
                </div>
            `;
        } else {
            resultsContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Error!</strong> ${result.message}
                </div>
            `;
        }
        
        // Re-enable form
        document.getElementById('submitBtn').disabled = false;
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('progressPanel').style.display = 'none';
        document.getElementById('resultsPanel').style.display = 'block';
        document.getElementById('resultsContent').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong>Error!</strong> Failed to process bulk update.
            </div>
        `;
        document.getElementById('submitBtn').disabled = false;
    });
});

function resetForm() {
    document.getElementById('bulkUpdateForm').reset();
    document.getElementById('progressPanel').style.display = 'none';
    document.getElementById('resultsPanel').style.display = 'none';
    updateSubmitText();
}

// Initialize
updateSubmitText();
</script>

{% endblock %}
