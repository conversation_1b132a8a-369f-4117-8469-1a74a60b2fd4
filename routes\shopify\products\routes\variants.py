import logging
from datetime import datetime
from flask import request, jsonify
from flask_login import login_required, current_user
from bson import ObjectId

from ..config import shopify_collection
from ..utils import (
    get_shopify_headers,
    get_shopify_store_url,
    create_shopify_session,
    get_location_id,
    update_product_variants
)

logger = logging.getLogger(__name__)

@login_required
def get_variants(shopify_id):
    """Get variants for a specific product"""
    try:
        shopify_id_int = int(shopify_id)
    except ValueError:
        logger.error(f"Invalid shopify_id format: {shopify_id}")
        return jsonify({'variants': [], 'error': None}), 200

    try:
        # Try to find product by either id or productId field
        product = shopify_collection.find_one({
            'username': current_user.username,
            '$or': [
                {'id': shopify_id_int},
                {'productId': str(shopify_id_int)}
            ]
        })
        
        if not product:
            logger.warning(f"Product not found for ID: {shopify_id}")
            return jsonify({'variants': [], 'error': None}), 200
            
        variants = product.get('variants', [])
        if not variants:
            logger.info(f"No variants found for product ID: {shopify_id}")
            return jsonify({'variants': [], 'error': None}), 200
        
        # If there's only one variant, mark it as pre-selected
        if len(variants) == 1 and isinstance(variants[0], dict):
            variants[0]['matched'] = True
        
        return jsonify({'variants': variants, 'error': None}), 200
        
    except Exception as e:
        logger.error(f"Error getting variants: {str(e)}")
        return jsonify({'error': f'An error occurred: {str(e)}'}), 500

@login_required
def update_variant():
    """Update variant price and quantity"""
    data = request.json
    product_id = data.get('productId')
    variant_id = data.get('variantId')
    new_price = data.get('price')
    new_quantity = data.get('quantity')

    if not all([product_id, variant_id, new_price is not None, new_quantity is not None]):
        return jsonify({'error': 'Missing required fields'}), 400

    try:
        # Get the product from MongoDB
        product = shopify_collection.find_one({
            '_id': ObjectId(product_id),
            'username': current_user.username
        })
        if not product:
            return jsonify({'error': 'Product not found'}), 404

        # Update variant in MongoDB
        updated_variants = []
        for variant in product['variants']:
            if str(variant['id']) == str(variant_id):
                variant['price'] = str(new_price)
                variant['inventory_quantity'] = new_quantity
            updated_variants.append(variant)

        # Only update MongoDB and mark for pushing to Shopify later
        shopify_collection.update_one(
            {'_id': ObjectId(product_id)},
            {'$set': {
                'variants': updated_variants,
                'needsPushing': True,
                'last_repriced': datetime.utcnow()
            }}
        )

        return jsonify({
            'message': 'Variant updated successfully',
            'new_price': new_price,
            'new_quantity': new_quantity
        })

    except Exception as e:
        logger.error(f"Error updating variant: {str(e)}")
        return jsonify({'error': f'An error occurred: {str(e)}'}), 500

@login_required
def update_price():
    """Update variant price only"""
    data = request.json
    product_id = data.get('productId')
    variant_id = data.get('variantId') 
    new_price = data.get('price')

    if not all([product_id, variant_id, new_price]):
        return jsonify({'error': 'Missing required fields'}), 400

    try:
        # Get the product from MongoDB
        product = shopify_collection.find_one({
            '_id': ObjectId(product_id),
            'username': current_user.username
        })
        if not product:
            return jsonify({'error': 'Product not found'}), 404

        # Update price in MongoDB
        updated_variants = []
        for variant in product['variants']:
            if str(variant['id']) == str(variant_id):
                variant['price'] = str(new_price)
            updated_variants.append(variant)

        # Only update MongoDB and mark for pushing to Shopify later
        shopify_collection.update_one(
            {'_id': ObjectId(product_id)},
            {'$set': {
                'variants': updated_variants,
                'needsPushing': True,
                'last_repriced': datetime.utcnow()
            }}
        )

        return jsonify({
            'message': 'Price updated successfully',
            'new_price': new_price
        })

    except Exception as e:
        logger.error(f"Error updating price: {str(e)}")
        return jsonify({'error': f'An error occurred: {str(e)}'}), 500
