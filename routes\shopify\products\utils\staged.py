import logging
from typing import Dict, List, Optional, Union
from datetime import datetime
from bson import ObjectId

logger = logging.getLogger(__name__)

def get_staged_inventory(username: str, db) -> List[Dict]:
    """
    Get staged inventory items for a user
    """
    try:
        query = {'username': username}
        staged_inventory = list(db.staged_inventory.find(query))
        
        processed_items = []
        for item in staged_inventory:
            try:
                if '_id' in item:
                    item['_id'] = str(item['_id'])
                processed_items.append(item)
            except Exception as e:
                logger.error(f"Error processing staged item: {str(e)}")
                continue
                
        return processed_items
        
    except Exception as e:
        logger.error(f"Error fetching staged inventory: {str(e)}")
        return []

def match_staged_variants(staged_item: Dict, shopify_product: Dict) -> Tuple[List[Dict], List[Dict]]:
    """
    Match staged variants with Shopify product variants
    Returns tuple of (matched_variants, unmatched_variants)
    """
    try:
        matched_variants = []
        unmatched_variants = []
        
        # Define condition normalization map
        condition_map = {
            'near mint': 'nm', 'near-mint': 'nm', 'nearmint': 'nm', 'nm': 'nm',
            'lightly played': 'lp', 'lightly-played': 'lp', 'lightlyplayed': 'lp', 'lp': 'lp',
            'moderately played': 'mp', 'moderately-played': 'mp', 'moderatelyplayed': 'mp', 'mp': 'mp',
            'heavily played': 'hp', 'heavily-played': 'hp', 'heavilyplayed': 'hp', 'hp': 'hp',
            'damaged': 'dm', 'dm': 'dm'
        }
        
        for staged_variant in staged_item.get('variants', []):
            staged_condition = staged_variant.get('condition', '').lower()
            staged_printing = staged_variant.get('printing', '').lower()
            match_found = False
            
            # Normalize the staged condition
            normalized_staged_condition = condition_map.get(staged_condition, staged_condition)
            
            for shopify_variant in shopify_product.get('variants', []):
                shopify_title = shopify_variant.get('title', '').lower()
                
                # Split the shopify title into parts to find the condition
                title_parts = shopify_title.split()
                
                # Try to find a matching condition in the title parts
                shopify_condition = None
                for part in title_parts:
                    normalized_part = condition_map.get(part, part)
                    if normalized_part in condition_map.values():
                        shopify_condition = normalized_part
                        break
                
                # If no condition found in parts, try normalizing the whole title
                if not shopify_condition:
                    shopify_condition = condition_map.get(shopify_title, shopify_title)
                
                # Check if both normalized conditions match exactly and printing type matches
                if (shopify_condition == normalized_staged_condition and 
                    staged_printing in shopify_title):
                    matched_variant = shopify_variant.copy()
                    matched_variant['staged_quantity'] = staged_variant.get('quantity', 0)
                    matched_variants.append(matched_variant)
                    match_found = True
                    logger.info(f"Matched variant: Staged {staged_variant}, Shopify {shopify_variant}")
                    break
            
            if not match_found:
                unmatched_variants.append(staged_variant)
                logger.warning(f"Unmatched variant: {staged_variant}")
                
        return matched_variants, unmatched_variants
        
    except Exception as e:
        logger.error(f"Error matching variants: {str(e)}")
        return [], []

def update_staged_variant_match(item_id: str, variant: Dict, db) -> bool:
    """
    Update a staged inventory item with a matched variant
    """
    try:
        result = db.staged_inventory.update_one(
            {'_id': ObjectId(item_id)},
            {'$set': {
                'shProduct.variant': variant,
                'matched_variant': True
            }}
        )
        return result.modified_count > 0
        
    except Exception as e:
        logger.error(f"Error updating staged variant match: {str(e)}")
        return False

def delete_staged_item(item_id: str, username: str, db) -> bool:
    """
    Delete a staged inventory item
    """
    try:
        result = db.staged_inventory.delete_one({
            "_id": ObjectId(item_id),
            "username": username
        })
        return result.deleted_count > 0
        
    except Exception as e:
        logger.error(f"Error deleting staged inventory item: {str(e)}")
        return False
