from optimized_mongo_connection import OptimizedMongoConnection
from config import Config

# Get the singleton instance of OptimizedMongoConnection
mongo = OptimizedMongoConnection.get_instance()

# The database connection should be obtained through the singleton
# Other parts of the application should use:
# from models.database import mongo
# db = mongo.db  # For MongoEngine
# pymongo_db = mongo.pymongo_db  # For PyMongo
