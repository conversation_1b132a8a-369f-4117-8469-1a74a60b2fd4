"""Model for eBay inventory."""
from datetime import datetime
from typing import Optional, List
from bson import ObjectId
from pymongo.database import Database
from pymongo.collection import Collection

class EbayInventory:
    def __init__(self, db: Database):
        self.collection: Collection = db['ebay_inventory']
        self.setup_indexes()

    def setup_indexes(self):
        """Setup required indexes for the collection."""
        self.collection.create_index([('user_id', 1), ('marketplace_id', 1), ('item_id', 1)], unique=True)
        self.collection.create_index('marketplace_id')
        self.collection.create_index('status')
        self.collection.create_index('updated_at')

    def save_item(self, item_data: dict, user_id: str, marketplace_id: str) -> bool:
        """Save or update an eBay item."""
        try:
            now = datetime.utcnow()
            
            # Extract relevant data from eBay API response
            inventory_item = {
                'user_id': user_id,
                'marketplace_id': marketplace_id,
                'item_id': item_data.get('sku') or str(item_data.get('itemId')),
                'title': item_data.get('title', ''),
                'price': float(item_data.get('price', {}).get('value', 0)),
                'currency': item_data.get('price', {}).get('currency', 'USD'),
                'quantity': int(item_data.get('quantity', 0)),
                'status': item_data.get('status', 'INACTIVE'),
                'condition': item_data.get('condition', ''),
                'ebay_data': item_data,  # Store complete eBay data for reference
                'updated_at': now
            }

            result = self.collection.update_one(
                {
                    'user_id': user_id,
                    'marketplace_id': marketplace_id,
                    'item_id': inventory_item['item_id']
                },
                {
                    '$set': inventory_item,
                    '$setOnInsert': {'created_at': now}
                },
                upsert=True
            )
            
            return bool(result.modified_count or result.upserted_id)
            
        except Exception as e:
            raise Exception(f"Failed to save eBay item: {str(e)}")

    def get_listings(self, user_id: str, marketplace_id: Optional[str] = None,
                    limit: int = 50, skip: int = 0) -> List[dict]:
        """Get eBay listings for a user."""
        query = {'user_id': user_id}
        if marketplace_id:
            query['marketplace_id'] = marketplace_id
            
        return list(self.collection.find(
            query,
            {
                'item_id': 1,
                'title': 1,
                'price': 1,
                'currency': 1,
                'quantity': 1,
                'status': 1,
                'marketplace_id': 1,
                'updated_at': 1
            }
        ).sort('updated_at', -1).skip(skip).limit(limit))

    def get_item(self, user_id: str, item_id: str, marketplace_id: str) -> Optional[dict]:
        """Get a specific eBay item."""
        return self.collection.find_one({
            'user_id': user_id,
            'marketplace_id': marketplace_id,
            'item_id': item_id
        })

    def delete_item(self, user_id: str, item_id: str, marketplace_id: str) -> bool:
        """Delete an eBay item."""
        result = self.collection.delete_one({
            'user_id': user_id,
            'marketplace_id': marketplace_id,
            'item_id': item_id
        })
        return result.deleted_count > 0

    def update_status(self, user_id: str, item_id: str, marketplace_id: str, 
                     status: str) -> bool:
        """Update an item's status."""
        result = self.collection.update_one(
            {
                'user_id': user_id,
                'marketplace_id': marketplace_id,
                'item_id': item_id
            },
            {
                '$set': {
                    'status': status,
                    'updated_at': datetime.utcnow()
                }
            }
        )
        return result.modified_count > 0

    def get_marketplace_stats(self, user_id: str, marketplace_id: str) -> dict:
        """Get statistics for a specific marketplace."""
        pipeline = [
            {
                '$match': {
                    'user_id': user_id,
                    'marketplace_id': marketplace_id
                }
            },
            {
                '$group': {
                    '_id': '$status',
                    'count': {'$sum': 1},
                    'total_value': {
                        '$sum': {'$multiply': ['$price', '$quantity']}
                    }
                }
            }
        ]
        
        results = list(self.collection.aggregate(pipeline))
        
        stats = {
            'total_items': 0,
            'active_items': 0,
            'total_value': 0,
            'by_status': {}
        }
        
        for result in results:
            status = result['_id']
            count = result['count']
            value = result['total_value']
            
            stats['total_items'] += count
            stats['total_value'] += value
            stats['by_status'][status] = {
                'count': count,
                'value': value
            }
            
            if status == 'ACTIVE':
                stats['active_items'] = count
        
        return stats
