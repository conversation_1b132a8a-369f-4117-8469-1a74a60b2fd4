from mongoengine import Document, <PERSON><PERSON>ield, <PERSON>loatField, DateTime<PERSON>ield, Reference<PERSON>ield, DynamicDocument
from models.user_model import User
from datetime import datetime

class Invoice(DynamicDocument):
    invoice_number = StringField(required=True, unique=True)
    date = DateTimeField(required=True)
    user = ReferenceField(User, required=True)
    subscription_type = StringField(choices=['Monthly', 'Annual', 'Lifetime'])
    payment_status = StringField(required=True, default='Unpaid', choices=['Unpaid', 'Payment Pending', 'Paid'])

    @classmethod
    def generate_invoice_number(cls):
        # Get current year and month
        now = datetime.now()
        year_month = now.strftime('%Y%m')
        
        # Find the latest invoice number for this year/month
        latest_invoice = cls.objects(invoice_number__startswith=f"INV{year_month}").order_by('-invoice_number').first()
        
        if latest_invoice:
            # Extract the sequence number and increment it
            sequence = int(latest_invoice.invoice_number[-4:]) + 1
        else:
            # Start with 1 if no invoices exist for this year/month
            sequence = 1
            
        # Format: INV2024010001
        return f"INV{year_month}{sequence:04d}"

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
        return super().save(*args, **kwargs)

    meta = {
        'collection': 'invoice',
        'indexes': [
            'invoice_number',
            'user',
            'date',
            'payment_status'
        ]
    }

    @property
    def total(self):
        return self.converted_price if hasattr(self, 'converted_price') else self.price

    @total.setter 
    def total(self, value):
        if hasattr(self, 'converted_price'):
            self.converted_price = value
        else:
            self.price = value

    @property
    def bank_details(self):
        bank_details = {
            'GBP': {
                'account_holder': 'TCG Sync',
                'sort_code': '23-08-01',
                'account_number': '********',
                'iban': 'GB64 TRWI 2308 0111 3206 61',
                'bank_name': 'Wise Payments Limited',
                'bank_address': '56 Shoreditch High Street\nLondon\nE1 6JJ\nUnited Kingdom'
            },
            'AUD': {
                'account_holder': 'TCG Sync',
                'bsb_code': '774001',
                'account_number': '*********'
            },
            'CAD': {
                'account_holder': 'TCG Sync',
                'institution_number': '621',
                'account_number': '************',
                'transit_number': '16001',
                'bank_name': 'Peoples Trust',
                'bank_address': '595 Burrard Street\nVancouver BC V7X 1L7\nCanada'
            },
            'EUR': {
                'account_holder': 'TCG Sync',
                'bic': 'TRWIBEB1XXX',
                'iban': 'BE32 9676 7345 5202',
                'bank_name': 'Wise',
                'bank_address': 'Rue du Trône 100, 3rd floor\nBrussels\n1050\nBelgium'
            },
            'USD': {
                'account_holder': 'TCG Sync',
                'routing_number': '*********',
                'account_number': '**********',
                'account_type': 'Checking',
                'bank_name': 'Community Federal Savings Bank',
                'bank_address': '89-16 Jamaica Ave\nWoodhaven NY 11421\nUnited States'
            }
        }
        currency = getattr(self, 'currency', getattr(self, 'currency_code', 'GBP'))
        return bank_details.get(currency, bank_details['GBP'])  # Default to GBP if currency not found

    def to_dict(self):
        total = getattr(self, 'total', None)
        if total is None:
            total = getattr(self, 'converted_price', getattr(self, 'original_price', getattr(self, 'price', 0)))
        
        return {
            'invoice_number': self.invoice_number,
            'date': self.date.strftime('%Y-%m-%d'),
            'subscription_type': self.subscription_type,
            'original_price': getattr(self, 'original_price', getattr(self, 'price', 0)),
            'original_currency': getattr(self, 'original_currency', 'GBP'),
            'converted_price': getattr(self, 'converted_price', getattr(self, 'price', 0)),
            'total': total,
            'currency': getattr(self, 'currency', getattr(self, 'currency_code', 'GBP')),
            'currency_symbol': getattr(self, 'currency_symbol', '£'),
            'user': {
                'name': self.user.name,
                'email': self.user.email
            },
            'bank_details': self.bank_details,
            'payment_status': self.payment_status
        }
