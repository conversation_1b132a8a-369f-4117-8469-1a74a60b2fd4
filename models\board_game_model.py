from mongoengine import Document, StringField, IntField, FloatField, ListField, BooleanField

class BoardGame(Document):
    id = IntField()
    name = StringField(required=True)
    yearpublished = IntField()
    rank = IntField()
    bayesaverage = FloatField()
    average = FloatField()
    usersrated = IntField()
    is_expansion = IntField()
    familygames_rank = IntField()
    partygames_rank = IntField()
    strategygames_rank = IntField()
    min_players = StringField()  # Changed to StringField as example shows "3"
    max_players = StringField()  # Changed to StringField as example shows "6"
    min_age = StringField()      # Changed to StringField as example shows "9"
    playing_time = StringField() # Changed to StringField as example shows "15"
    description = StringField()
    image_url = StringField()
    designers = ListField(StringField())
    artists = ListField(StringField())
    publishers = ListField(StringField())
    mechanics = ListField(StringField())
    categories = ListField(StringField())
    weight = FloatField()

    meta = {
        'db_alias': 'test',
        'collection': 'boardgames',
        'indexes': [
            'name',
            'yearpublished',
            'rank',
            'average',
            'min_players',
            'max_players',
            'playing_time',
            'weight',
            'categories',
            'mechanics',
            'designers',
            'familygames_rank',
            'partygames_rank',
            'strategygames_rank'
        ]
    }
