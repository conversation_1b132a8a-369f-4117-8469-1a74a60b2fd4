from mongoengine import Document, EmbeddedDocument, StringField, DateTimeField, FloatField, IntField, ListField, EmbeddedDocumentField, BooleanField
from datetime import datetime

class LineItem(EmbeddedDocument):
    product_id = StringField(required=True)
    variant_id = StringField(required=True)
    title = StringField(required=True)
    quantity = IntField(required=True, min_value=1)
    price = FloatField(required=True)
    sku = StringField()
    vendor = StringField()

class ShippingAddress(EmbeddedDocument):
    name = StringField(required=True)
    address1 = StringField(required=True)
    address2 = StringField()
    city = StringField(required=True)
    province = StringField()
    country = StringField(required=True)
    zip = StringField(required=True)
    phone = StringField()

class Order(Document):
    order_number = StringField(required=True, unique=True)
    email = StringField(required=True)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    total_price = FloatField(required=True)
    subtotal_price = FloatField(required=True)
    total_tax = FloatField()
    currency = StringField(required=True)
    financial_status = StringField(required=True)  # paid, pending, refunded, etc.
    fulfillment_status = StringField()  # fulfilled, partial, unfulfilled
    line_items = ListField(EmbeddedDocumentField(LineItem))
    shipping_address = EmbeddedDocumentField(ShippingAddress)
    note = StringField()
    tags = ListField(StringField())
    source_name = StringField()  # e.g., "web", "pos", "shopify_draft_order", etc.
    
    # Fields specific to your application
    username = StringField(required=True)  # To associate the order with a user in your system
    is_shipped = BooleanField(default=False)
    tracking_number = StringField()
    shipping_company = StringField()

    meta = {
        'collection': 'orders',
        'ordering': ['-created_at'],
        'indexes': [
            'order_number',
            'email',
            'username',
            'created_at',
            'financial_status',
            'fulfillment_status'
        ]
    }

    def __str__(self):
        return f"Order {self.order_number} - {self.email} - {self.total_price} {self.currency}"
