from mongoengine import Document, IntField, StringField, ListField, EmbeddedDocument, EmbeddedDocumentField, DateTimeField

class Localization(EmbeddedDocument):
    name = StringField()
    idLanguage = IntField()
    languageName = StringField()

class Link(EmbeddedDocument):
    rel = StringField()
    href = StringField()
    method = StringField()

class CMProduct(Document):
    idProduct = IntField()
    idMetaproduct = IntField()
    countReprints = IntField()
    enName = StringField()
    locName = StringField()
    localization = ListField(EmbeddedDocumentField(Localization))
    website = StringField()
    image = StringField()
    gameName = StringField()
    categoryName = StringField()
    idGame = IntField()
    number = StringField()
    rarity = StringField()
    expansionName = StringField()
    expansionIcon = IntField()
    countArticles = IntField()
    countFoils = IntField()
    links = ListField(EmbeddedDocumentField(Link))
    gameAbbreviation = StringField()
    expansionAbbreviation = StringField()
    createdAt = StringField()
    dateAdded = StringField()
    fileType = StringField()
    idCategory = IntField()
    idExpansion = IntField()
    idMetacard = IntField()
    name = StringField()
    version = IntField()

    meta = {
        'collection': 'cmSingles',
        'indexes': [
            'idProduct',
            'enName',
            'name',
            {
                'fields': ['$enName', '$name'],
                'default_language': 'english',
                'weights': {'enName': 10, 'name': 5}
            }
        ]
    }

    @classmethod
    def ensure_indexes(cls):
        """Ensure that all required indexes exist in the database"""
        # Let mongoengine handle index creation through meta configuration
        pass
