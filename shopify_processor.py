import os
import json
import time
import requests
import shopify
from pymongo import MongoClient
import logging
from datetime import datetime
import sys
import shutil
import threading
import queue
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor

# Configuration
BASE_DIR = "/var/www/html/shopify_json_files/"
MONGODB_URI = "mongodb://admin:Reggie2805!@147.93.87.204:27017/admin?authSource=admin"
DATABASE_NAME = "test"
USERS_COLLECTION = "user"
PUSHED_COLLECTION = "pushed"
PUSH_RESULTS_COLLECTION = "push_results"  # Collection to store detailed results for each item
CHECK_INTERVAL = 180  # 15 minutes in seconds
MAX_THREADS = 5  # Maximum number of concurrent threads
MAX_FILE_SIZE = 15 * 1024 * 1024  # 15MB in bytes
TOKEN_COOLDOWN = 1  # 1 second cooldown between token uses

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# MongoDB connection
try:
    mongo_client = MongoClient(MONGODB_URI)
    db = mongo_client[DATABASE_NAME]
    users_collection = db[USERS_COLLECTION]
    pushed_collection = db[PUSHED_COLLECTION]
    push_results_collection = db[PUSH_RESULTS_COLLECTION]
    print("Successfully connected to MongoDB")
    logger.info("Successfully connected to MongoDB")
except Exception as e:
    print(f"Failed to connect to MongoDB: {str(e)}")
    logger.error(f"Failed to connect to MongoDB: {str(e)}")
    sys.exit(1)

@dataclass
class TokenState:
    token: str
    index: int
    is_busy: bool = False
    last_used: float = 0
    active_jobs: int = 0
    cooldown_until: float = 0  # Timestamp when token can be used again

class TokenManager:
    def __init__(self):
        self.tokens: Dict[str, Dict[str, TokenState]] = {}  # username -> {token -> TokenState}
        self._lock = threading.Lock()
        self.token_indices: Dict[str, Dict[str, int]] = {}  # username -> {token -> index}

    def load_user_tokens(self, user_data: Dict[str, Any]) -> List[str]:
        username = user_data.get('username')
        available_tokens = []

        with self._lock:
            self.tokens[username] = {}
            self.token_indices[username] = {}

            for i in range(6):
                token_key = f"shopifyAccessToken{i if i > 0 else ''}"
                if token := user_data.get(token_key):
                    self.tokens[username][token] = TokenState(token=token, index=i)
                    self.token_indices[username][token] = i
                    available_tokens.append(token)

        print(f"Loaded {len(available_tokens)} tokens for {username}")
        logger.info(f"Loaded {len(available_tokens)} tokens for {username}")
        return available_tokens

    def get_available_token(self, username: str) -> Optional[Tuple[str, int]]:
        """Get a token that is not currently busy or in cooldown"""
        if username not in self.tokens:
            return None

        current_time = time.time()

        with self._lock:
            for token, state in self.tokens[username].items():
                # Check if token is not busy, not in cooldown, and regular cooldown period has passed
                if (current_time >= state.cooldown_until and
                    current_time - state.last_used >= TOKEN_COOLDOWN and
                    not state.is_busy):
                    state.last_used = current_time
                    state.is_busy = True
                    return token, state.index

        return None

    def get_all_available_tokens(self, username: str) -> List[Tuple[str, int]]:
        """Get all tokens that are not currently busy or in cooldown"""
        if username not in self.tokens:
            return []

        available_tokens = []
        current_time = time.time()

        with self._lock:
            for token, state in self.tokens[username].items():
                # Check if token is not busy, not in cooldown, and regular cooldown period has passed
                if (current_time >= state.cooldown_until and
                    current_time - state.last_used >= TOKEN_COOLDOWN and
                    not state.is_busy):
                    state.last_used = current_time
                    state.is_busy = True
                    available_tokens.append((token, state.index))

        return available_tokens

    def set_token_cooldown(self, username: str, token: str, minutes: int = 30):
        """Set a token to cooldown for the specified number of minutes"""
        with self._lock:
            if username in self.tokens and token in self.tokens[username]:
                state = self.tokens[username][token]
                state.cooldown_until = time.time() + (minutes * 60)
                state.is_busy = False  # Make sure to release the busy state
                print(f"Set Token_{state.index} for {username} on cooldown for {minutes} minutes")
                logger.info(f"Set Token_{state.index} for {username} on cooldown for {minutes} minutes")

    def release_token(self, username: str, token: str):
        """Release a token after use"""
        with self._lock:
            if username in self.tokens and token in self.tokens[username]:
                state = self.tokens[username][token]
                state.is_busy = False
                print(f"Released Token_{state.index} for {username}")
                logger.info(f"Released Token_{state.index} for {username}")

def process_product_data(product_data):
    if 'input' in product_data:
        # Move media out of input if it exists
        if 'images' in product_data['input']:
            product_data['media'] = [
                {
                    "originalSource": img['src'],
                    "alt": f"Image for {product_data['input'].get('title', 'Product')}",
                    "mediaContentType": "IMAGE"
                } for img in product_data['input']['images']
            ]
            del product_data['input']['images']

        # Process other fields as needed
        # ...

    return product_data

def convert_json_to_jsonl(json_file_path):
    jsonl_file_path = json_file_path.rsplit('.', 1)[0] + '.jsonl'
    print(f"Converting {json_file_path} to JSONL format: {jsonl_file_path}")
    logger.info(f"Converting {json_file_path} to JSONL format: {jsonl_file_path}")

    try:
        with open(json_file_path, 'r') as json_file, open(jsonl_file_path, 'w') as jsonl_file:
            data = json.load(json_file)
            for item in data:
                processed_item = process_product_data(item)
                json.dump(processed_item, jsonl_file)
                jsonl_file.write('\n')
        return jsonl_file_path
    except Exception as e:
        print(f"Error converting JSON to JSONL: {str(e)}")
        logger.error(f"Error converting JSON to JSONL: {str(e)}")
        return None

def verify_shopify_connection(store, token):
    shop_url = f"https://{store}.myshopify.com/admin/api/2023-10/graphql.json"
    session = shopify.Session(shop_url, '2023-10', token)
    shopify.ShopifyResource.activate_session(session)
    graphql_client = shopify.GraphQL()

    try:
        # Simple query to check if we can connect to the shop
        query = '''
        {
            shop {
                name
            }
        }
        '''
        result = graphql_client.execute(query)
        result_dict = json.loads(result)
        if 'data' in result_dict and 'shop' in result_dict['data']:
            print(f"Successfully connected to Shopify store: {result_dict['data']['shop']['name']}")
            logger.info(f"Successfully connected to Shopify store: {result_dict['data']['shop']['name']}")
            return True
        else:
            print("Failed to retrieve shop information")
            logger.error("Failed to retrieve shop information")
            return False
    except Exception as e:
        print(f"Error verifying Shopify connection: {str(e)}")
        logger.error(f"Error verifying Shopify connection: {str(e)}")
        return False
    finally:
        shopify.ShopifyResource.clear_session()

def split_large_file(file_path):
    base_name = os.path.splitext(os.path.basename(file_path))[0]
    dir_name = os.path.dirname(file_path)
    file_number = 1
    current_size = 0
    current_data = []
    split_files = []

    with open(file_path, 'r') as f:
        for line in f:
            line_size = len(line.encode('utf-8'))
            if current_size + line_size > MAX_FILE_SIZE:
                # Write current data to a new file
                new_file_path = os.path.join(dir_name, f"{base_name}_part{file_number}.jsonl")
                with open(new_file_path, 'w') as out_file:
                    for item in current_data:
                        json.dump(item, out_file)
                        out_file.write('\n')
                split_files.append(new_file_path)

                # Reset for next file
                current_data = []
                current_size = 0
                file_number += 1

            current_data.append(json.loads(line))
            current_size += line_size

    # Write any remaining data
    if current_data:
        new_file_path = os.path.join(dir_name, f"{base_name}_part{file_number}.jsonl")
        with open(new_file_path, 'w') as out_file:
            for item in current_data:
                json.dump(item, out_file)
                out_file.write('\n')
        split_files.append(new_file_path)

    return split_files

def check_existing_bulk_operations(graphql_client):
    query = '''
    {
      currentBulkOperation {
        id
        status
        errorCode
        createdAt
        completedAt
        objectCount
        fileSize
        url
        partialDataUrl
      }
    }
    '''

    try:
        result = graphql_client.execute(query)
        result_dict = json.loads(result)
        current_operation = result_dict.get('data', {}).get('currentBulkOperation')

        if current_operation and current_operation['status'] in ['CREATED', 'RUNNING']:
            print(f"Existing bulk operation in progress: {current_operation['id']}")
            logger.info(f"Existing bulk operation in progress: {current_operation['id']}")
            return True

        return False
    except Exception as e:
        print(f"Error checking for existing bulk operations: {str(e)}")
        logger.error(f"Error checking for existing bulk operations: {str(e)}")
        return True  # Assume there's an operation in progress if we can't check

def upload_file(file_path, store, token, username, token_index=0):
    print(f"Uploading file: {file_path} with Token_{token_index}")
    logger.info(f"Uploading file: {file_path} with Token_{token_index}")
    shop_url = f"https://{store}.myshopify.com/admin/api/2023-10/graphql.json"
    session = shopify.Session(shop_url, '2023-10', token)
    shopify.ShopifyResource.activate_session(session)
    graphql_client = shopify.GraphQL()

    try:
        # Step 1: Create staged upload
        print("Creating staged upload...")
        logger.info("Creating staged upload...")
        staged_upload_query = '''
        mutation {
            stagedUploadsCreate(input: {
                resource: BULK_MUTATION_VARIABLES,
                filename: "output.jsonl",
                mimeType: "application/jsonl",
                httpMethod: POST
            }) {
                userErrors {
                    field,
                    message
                },
                stagedTargets {
                    url,
                    resourceUrl,
                    parameters {
                        name,
                        value
                    }
                }
            }
        }
        '''

        result = graphql_client.execute(staged_upload_query)
        result_dict = json.loads(result)

        if 'userErrors' in result_dict['data']['stagedUploadsCreate'] and result_dict['data']['stagedUploadsCreate']['userErrors']:
            print(f"Staged upload creation failed: {result_dict['data']['stagedUploadsCreate']['userErrors']}")
            logger.error(f"Staged upload creation failed: {result_dict['data']['stagedUploadsCreate']['userErrors']}")
            return False

        # Step 2: Upload file to URL
        print("Uploading file to staged URL...")
        logger.info("Uploading file to staged URL...")
        upload_url = result_dict['data']['stagedUploadsCreate']['stagedTargets'][0]['url']
        upload_params = {param['name']: param['value'] for param in result_dict['data']['stagedUploadsCreate']['stagedTargets'][0]['parameters']}

        with open(file_path, 'rb') as file:
            response = requests.post(upload_url, files={'file': file}, data=upload_params)

        if response.status_code != 201:
            print(f"File upload failed: {response.status_code}, {response.text}")
            logger.error(f"File upload failed: {response.status_code}, {response.text}")
            return False

        # Step 3: Start bulk operation
        print("Starting bulk operation...")
        logger.info("Starting bulk operation...")
        staged_upload_path = response.text.split('<Key>')[1].split('</Key>')[0]
        bulk_operation_query = f'''
        mutation {{
            bulkOperationRunMutation(
                mutation: """
                mutation call($input: ProductInput!, $media: [CreateMediaInput!]) {{
                    productCreate(input: $input, media: $media) {{
                        product {{
                            id
                            title
                            media {{
                                edges {{
                                    node {{
                                        id
                                        alt
                                        mediaContentType
                                    }}
                                }}
                            }}
                        }}
                        userErrors {{
                            field
                            message
                        }}
                    }}
                }}
                """,
                stagedUploadPath: "{staged_upload_path}"
            ) {{
                bulkOperation {{
                    id
                    status
                }}
                userErrors {{
                    field
                    message
                }}
            }}
        }}
        '''

        result = graphql_client.execute(bulk_operation_query)
        result_dict = json.loads(result)

        if 'userErrors' in result_dict['data']['bulkOperationRunMutation'] and result_dict['data']['bulkOperationRunMutation']['userErrors']:
            print(f"Bulk operation initiation failed: {result_dict['data']['bulkOperationRunMutation']['userErrors']}")
            logger.error(f"Bulk operation initiation failed: {result_dict['data']['bulkOperationRunMutation']['userErrors']}")
            return False

        operation_id = result_dict['data']['bulkOperationRunMutation']['bulkOperation']['id']

        # Step 4: Poll for bulk operation completion
        print(f"Polling for bulk operation completion. Operation ID: {operation_id}")
        logger.info(f"Polling for bulk operation completion. Operation ID: {operation_id}")

        # Get file size for adaptive polling
        file_size = os.path.getsize(file_path)
        print(f"File size: {file_size/1024/1024:.2f}MB")
        logger.info(f"File size: {file_size/1024/1024:.2f}MB")

        if poll_bulk_operation(graphql_client, operation_id, username, store, token, file_size, token_index):
            print(f"Successfully processed file: {file_path}")
            logger.info(f"Successfully processed file: {file_path}")
            return True
        else:
            print(f"Failed to process file: {file_path}")
            logger.error(f"Failed to process file: {file_path}")
            return False

    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
        logger.error(f"HTTP error occurred: {http_err}")
        print(f"Shop URL: {shop_url}")
        logger.error(f"Shop URL: {shop_url}")
        print(f"Please check if the store name '{store}' is correct and the access token is valid.")
        logger.error(f"Please check if the store name '{store}' is correct and the access token is valid.")
        return False
    except Exception as e:
        print(f"Error processing file {file_path}: {str(e)}")
        logger.error(f"Error processing file {file_path}: {str(e)}")
        return False
    finally:
        shopify.ShopifyResource.clear_session()

def download_and_process_results(url, operation_id, username):
    """
    Download and process the results file from a completed bulk operation
    """
    print(f"Downloading results file from: {url}")
    logger.info(f"Downloading results file from: {url}")

    try:
        response = requests.get(url)
        if response.status_code != 200:
            print(f"Failed to download results file: {response.status_code}, {response.text}")
            logger.error(f"Failed to download results file: {response.status_code}, {response.text}")
            return False

        # Process the JSONL file line by line
        results = []
        success_count = 0
        error_count = 0

        for line in response.text.splitlines():
            if not line.strip():
                continue

            try:
                result = json.loads(line)

                # Check if this is a successful operation or an error
                if 'data' in result and any(result['data'].values()):
                    success_count += 1
                    status = "success"
                elif 'errors' in result:
                    error_count += 1
                    status = "error"
                else:
                    status = "unknown"

                # Store the result with metadata
                result_doc = {
                    "operationId": operation_id,
                    "username": username,
                    "timestamp": datetime.now(),
                    "status": status,
                    "result": result
                }

                # Insert into MongoDB
                push_results_collection.insert_one(result_doc)
                results.append(result_doc)

            except json.JSONDecodeError:
                print(f"Error parsing result line: {line}")
                logger.error(f"Error parsing result line: {line}")
                continue

        print(f"Processed {len(results)} results: {success_count} successful, {error_count} errors")
        logger.info(f"Processed {len(results)} results: {success_count} successful, {error_count} errors")

        # Update the pushed collection record with summary information
        pushed_collection.update_one(
            {"operationId": operation_id},
            {"$set": {
                "resultsProcessed": len(results),
                "successCount": success_count,
                "errorCount": error_count,
                "resultsProcessedAt": datetime.now()
            }}
        )

        return True
    except Exception as e:
        print(f"Error processing results file: {str(e)}")
        logger.error(f"Error processing results file: {str(e)}")
        return False

def poll_bulk_operation(shopify_client, operation_id, username, store, token, file_size=None, token_index=0):
    poll_query = f'''
    {{
        node(id: "{operation_id}") {{
            ... on BulkOperation {{
                id
                status
                errorCode
                createdAt
                completedAt
                objectCount
                fileSize
                url
            }}
        }}
    }}
    '''

    # Calculate polling interval based on file size
    if file_size is None:
        # Default polling interval if file size is unknown
        poll_interval = 15  # 15 seconds
    else:
        # Scale polling interval based on file size
        # Minimum 5 seconds for small files, maximum 60 seconds for large files
        # 1MB = ~5 seconds, 10MB = ~30 seconds, 15MB = ~60 seconds
        poll_interval = max(5, min(60, int(file_size / (1024 * 1024) * 4)))

    print(f"Using poll interval of {poll_interval} seconds based on file size of {file_size/1024/1024:.2f}MB" if file_size else f"Using default poll interval of {poll_interval} seconds")
    logger.info(f"Using poll interval of {poll_interval} seconds based on file size of {file_size/1024/1024:.2f}MB" if file_size else f"Using default poll interval of {poll_interval} seconds")

    # Calculate max attempts based on poll interval to maintain a reasonable total wait time
    # Total wait time = 15 minutes (900 seconds)
    max_attempts = int(900 / poll_interval)
    for attempt in range(max_attempts):
        result = shopify_client.execute(poll_query)
        result_dict = json.loads(result)
        status = result_dict['data']['node']['status']

        print(f"Bulk operation status: {status}, Attempt: {attempt + 1}")
        logger.info(f"Bulk operation status: {status}, Attempt: {attempt + 1}")

        if status == 'COMPLETED':
            print(f"Bulk operation completed: {operation_id}")
            logger.info(f"Bulk operation completed: {operation_id}")
            # Insert the basic operation info
            push_doc = {
                "username": username,
                "operationId": operation_id,
                "status": status,
                "completedAt": result_dict['data']['node']['completedAt'],
                "objectCount": result_dict['data']['node']['objectCount'],
                "fileSize": result_dict['data']['node']['fileSize'],
                "url": result_dict['data']['node']['url'],
                "timestamp": datetime.now(),
                "storeName": store,
                "apiKey": token,
                "tokenIndex": token_index
            }
            pushed_collection.insert_one(push_doc)

            # Download and process the detailed results
            if result_dict['data']['node']['url']:
                download_and_process_results(
                    result_dict['data']['node']['url'],
                    operation_id,
                    username
                )

            return True
        elif status == 'FAILED':
            print(f"Bulk operation failed: {operation_id}, Error: {result_dict['data']['node']['errorCode']}")
            logger.error(f"Bulk operation failed: {operation_id}, Error: {result_dict['data']['node']['errorCode']}")
            return False
        elif status in ['CREATED', 'RUNNING']:
            print(f"Bulk operation in progress: {operation_id}, Status: {status}")
            logger.info(f"Bulk operation in progress: {operation_id}, Status: {status}")
            time.sleep(poll_interval)  # Wait based on calculated interval
        else:
            print(f"Unknown status for bulk operation: {operation_id}, Status: {status}")
            logger.warning(f"Unknown status for bulk operation: {operation_id}, Status: {status}")
            return False

    print(f"Bulk operation timed out: {operation_id}")
    logger.error(f"Bulk operation timed out: {operation_id}")
    return False

def process_file(file_info):
    """Process a single file with the given token"""
    username, user_dir, filename, file_path, token, token_index, store, is_split_file = file_info

    processed_dir = os.path.join(user_dir, 'processed')
    failed_dir = os.path.join(user_dir, 'failed')
    in_progress_dir = os.path.join(user_dir, 'in_progress')

    try:
        # Check if this token has an existing bulk operation
        shop_url = f"https://{store}.myshopify.com/admin/api/2023-10/graphql.json"
        session = shopify.Session(shop_url, '2023-10', token)
        shopify.ShopifyResource.activate_session(session)
        graphql_client = shopify.GraphQL()

        if check_existing_bulk_operations(graphql_client):
            print(f"Token_{token_index} has an existing bulk operation, setting on cooldown")
            logger.info(f"Token_{token_index} has an existing bulk operation, setting on cooldown")
            shopify.ShopifyResource.clear_session()
            return "cooldown", token, token_index, username, file_path

        shopify.ShopifyResource.clear_session()

        # Process the file with this token
        if upload_file(file_path, store, token, username, token_index):
            if is_split_file:
                os.remove(file_path)
                print(f"Processed and removed split file: {file_path} with Token_{token_index}")
                logger.info(f"Processed and removed split file: {file_path} with Token_{token_index}")
            else:
                print(f"Successfully processed file: {file_path} with Token_{token_index}")
                logger.info(f"Successfully processed file: {file_path} with Token_{token_index}")
            return "success", token, token_index, username, file_path
        else:
            print(f"Failed to process file: {file_path} with Token_{token_index}")
            logger.error(f"Failed to process file: {file_path} with Token_{token_index}")
            if is_split_file:
                # Copy the split file to the failed directory
                failed_file_path = os.path.join(failed_dir, os.path.basename(file_path))
                shutil.copy(file_path, failed_file_path)
                print(f"Copied failed split file to: {failed_file_path}")
                logger.info(f"Copied failed split file to: {failed_file_path}")
            else:
                # Move the file to the failed directory
                failed_path = os.path.join(failed_dir, filename)
                if os.path.exists(os.path.join(in_progress_dir, filename)):
                    shutil.move(os.path.join(in_progress_dir, filename), failed_path)
                    print(f"Moved file to failed directory: {failed_path}")
                    logger.info(f"Moved file to failed directory: {failed_path}")
            return "failed", token, token_index, username, file_path
    except Exception as e:
        print(f"Error processing file {file_path} with Token_{token_index}: {str(e)}")
        logger.error(f"Error processing file {file_path} with Token_{token_index}: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return "error", token, token_index, username, file_path

def process_user_files(username, user_dir):
    print(f"Processing files for user: {username}")
    logger.info(f"Processing files for user: {username}")

    user_data = users_collection.find_one({"username": username})
    if not user_data or 'shopifyStoreName' not in user_data:
        print(f"Shopify settings not found for user: {username}")
        logger.error(f"Shopify settings not found for user: {username}")
        return

    # Check if user has at least one token
    has_token = False
    for i in range(6):
        token_key = f"shopifyAccessToken{i if i > 0 else ''}"
        if token_key in user_data and user_data[token_key]:
            has_token = True
            break

    if not has_token:
        print(f"No Shopify access tokens found for user: {username}")
        logger.error(f"No Shopify access tokens found for user: {username}")
        return

    store = user_data['shopifyStoreName']
    print(f"Found Shopify settings for user {username}: store={store}")
    logger.info(f"Found Shopify settings for user {username}: store={store}")

    # Initialize token manager and load all available tokens
    token_manager = TokenManager()
    available_tokens = token_manager.load_user_tokens(user_data)

    if not available_tokens:
        print(f"No valid Shopify tokens found for user: {username}")
        logger.error(f"No valid Shopify tokens found for user: {username}")
        return

    # Verify connection with the first token
    first_token = available_tokens[0]
    if not verify_shopify_connection(store, first_token):
        print(f"Skipping processing for user {username} due to Shopify connection issues")
        logger.error(f"Skipping processing for user {username} due to Shopify connection issues")
        return

    processed_dir = os.path.join(user_dir, 'processed')
    os.makedirs(processed_dir, exist_ok=True)

    in_progress_dir = os.path.join(user_dir, 'in_progress')
    os.makedirs(in_progress_dir, exist_ok=True)

    failed_dir = os.path.join(user_dir, 'failed')
    os.makedirs(failed_dir, exist_ok=True)

    # First process regular files
    files = sorted([f for f in os.listdir(user_dir) if f not in ['processed', 'in_progress', 'failed']])
    print(f"Files in user directory: {files}")
    logger.info(f"Files in user directory: {files}")

    # Process all files in parallel using all available tokens
    while files:  # Continue until all files are processed
        # Get all available tokens
        available_tokens = token_manager.get_all_available_tokens(username)
        if not available_tokens:
            print(f"No available tokens for user {username}, waiting...")
            logger.info(f"No available tokens for user {username}, waiting...")
            time.sleep(5)  # Wait a bit and try again
            continue

        print(f"Found {len(available_tokens)} available tokens for processing files")
        logger.info(f"Found {len(available_tokens)} available tokens for processing files")

        # Prepare files to process in parallel
        files_to_process = []
        for i, (token, token_index) in enumerate(available_tokens):
            if i < len(files):  # Make sure we have a file to process
                filename = files[i]
                if filename.endswith('.json') or filename.endswith('.jsonl'):
                    file_path = os.path.join(user_dir, filename)
                    print(f"Processing file: {file_path} with Token_{token_index}")
                    logger.info(f"Processing file: {file_path} with Token_{token_index}")

                    # Move file to in_progress directory
                    in_progress_path = os.path.join(in_progress_dir, filename)
                    shutil.move(file_path, in_progress_path)
                    print(f"Moved file to in_progress directory: {in_progress_path}")
                    logger.info(f"Moved file to in_progress directory: {in_progress_path}")

                    # Convert JSON to JSONL if necessary
                    if filename.endswith('.json'):
                        jsonl_file_path = convert_json_to_jsonl(in_progress_path)
                        if not jsonl_file_path:
                            continue  # Skip this file if conversion failed
                    else:
                        jsonl_file_path = in_progress_path

                    files_to_process.append((username, user_dir, filename, jsonl_file_path, token, token_index, store, False))
                    # Remove from files list
                    files.remove(filename)

        if not files_to_process:
            # No valid files to process
            break

        # Process files in parallel
        results = []
        with ThreadPoolExecutor(max_workers=len(files_to_process)) as executor:
            futures = [executor.submit(process_file, file_info) for file_info in files_to_process]
            for future in futures:
                results.append(future.result())

        # Handle results
        for result, file_info in zip(results, files_to_process):
            status, token, token_index, _, _ = result
            _, _, filename, jsonl_file_path, _, _, _, _ = file_info
            in_progress_path = os.path.join(in_progress_dir, filename)

            if status == "cooldown":
                token_manager.set_token_cooldown(username, token, 30)  # 30 minute cooldown
            else:
                token_manager.release_token(username, token)

            if status == "success":
                # Move file to processed directory
                processed_path = os.path.join(processed_dir, filename)
                if os.path.exists(in_progress_path):
                    shutil.move(in_progress_path, processed_path)
                    print(f"Moved file to processed directory: {processed_path}")
                    logger.info(f"Moved file to processed directory: {processed_path}")
                # Clean up JSONL file if it's different from the original file
                if jsonl_file_path and jsonl_file_path != in_progress_path and os.path.exists(jsonl_file_path):
                    os.remove(jsonl_file_path)
                    print(f"Removed temporary JSONL file: {jsonl_file_path}")
                    logger.info(f"Removed temporary JSONL file: {jsonl_file_path}")
            elif status == "failed":
                # Move the file to the failed directory
                failed_path = os.path.join(failed_dir, filename)
                if os.path.exists(in_progress_path):
                    shutil.move(in_progress_path, failed_path)
                    print(f"Moved file to failed directory: {failed_path}")
                    logger.info(f"Moved file to failed directory: {failed_path}")
                # Clean up JSONL file if it's different from the original file
                if jsonl_file_path and jsonl_file_path != in_progress_path and os.path.exists(jsonl_file_path):
                    os.remove(jsonl_file_path)
                    print(f"Removed temporary JSONL file after failure: {jsonl_file_path}")
                    logger.info(f"Removed temporary JSONL file after failure: {jsonl_file_path}")

            # Check file size and split if necessary
            if jsonl_file_path and os.path.exists(jsonl_file_path) and os.path.getsize(jsonl_file_path) > MAX_FILE_SIZE:
                print(f"File {jsonl_file_path} exceeds 15MB. Splitting...")
                logger.info(f"File {jsonl_file_path} exceeds 15MB. Splitting...")
                split_files = split_large_file(jsonl_file_path)

                # Process split files in parallel using all available tokens
                while split_files:  # Continue until all split files are processed
                    # Get all available tokens
                    available_tokens = token_manager.get_all_available_tokens(username)
                    if not available_tokens:
                        print(f"No available tokens for user {username}, waiting...")
                        logger.info(f"No available tokens for user {username}, waiting...")
                        time.sleep(5)  # Wait a bit and try again
                        continue

                    print(f"Found {len(available_tokens)} available tokens for processing split files")
                    logger.info(f"Found {len(available_tokens)} available tokens for processing split files")

                    # Prepare file info for parallel processing
                    file_infos = []
                    for i, (token, token_index) in enumerate(available_tokens):
                        if i < len(split_files):  # Make sure we have a file to process
                            file_infos.append((username, user_dir, os.path.basename(split_files[i]),
                                             split_files[i], token, token_index, store, True))

                    # Process files in parallel
                    results = []
                    with ThreadPoolExecutor(max_workers=len(file_infos)) as executor:
                        futures = [executor.submit(process_file, file_info) for file_info in file_infos]
                        for future in futures:
                            results.append(future.result())

                    # Handle results and release/cooldown tokens as needed
                    for result in results:
                        status, token, token_index, _, file_path = result
                        if status == "cooldown":
                            token_manager.set_token_cooldown(username, token, 30)  # 30 minute cooldown
                        else:
                            token_manager.release_token(username, token)

                        # Remove processed files from the list
                        if file_path in split_files:
                            split_files.remove(file_path)

    # After processing all regular files, check for failed files to retry
    failed_files = sorted(os.listdir(failed_dir))
    if failed_files:
        print(f"Found {len(failed_files)} failed files to retry for user {username}")
        logger.info(f"Found {len(failed_files)} failed files to retry for user {username}")

        # Filter for JSON and JSONL files
        failed_files = [f for f in failed_files if f.endswith('.json') or f.endswith('.jsonl')]

        # Process failed files in parallel using all available tokens
        while failed_files:  # Continue until all failed files are processed
            # Get all available tokens
            available_tokens = token_manager.get_all_available_tokens(username)
            if not available_tokens:
                print(f"No available tokens for user {username}, waiting...")
                logger.info(f"No available tokens for user {username}, waiting...")
                time.sleep(5)  # Wait a bit and try again
                continue

            print(f"Found {len(available_tokens)} available tokens for retrying failed files")
            logger.info(f"Found {len(available_tokens)} available tokens for retrying failed files")

            # Move files to in_progress for retry
            files_to_process = []
            for i, (token, token_index) in enumerate(available_tokens):
                if i < len(failed_files):  # Make sure we have a file to process
                    filename = failed_files[i]
                    failed_file_path = os.path.join(failed_dir, filename)
                    in_progress_path = os.path.join(in_progress_dir, filename)

                    print(f"Retrying failed file: {failed_file_path} with Token_{token_index}")
                    logger.info(f"Retrying failed file: {failed_file_path} with Token_{token_index}")

                    # Move to in_progress for retry
                    shutil.move(failed_file_path, in_progress_path)
                    print(f"Moved failed file to in_progress for retry: {in_progress_path}")
                    logger.info(f"Moved failed file to in_progress for retry: {in_progress_path}")

                    files_to_process.append((username, user_dir, filename, in_progress_path, token, token_index, store, False))

            # Process files in parallel
            results = []
            with ThreadPoolExecutor(max_workers=len(files_to_process)) as executor:
                futures = [executor.submit(process_file, file_info) for file_info in files_to_process]
                for future in futures:
                    results.append(future.result())

            # Handle results
            for result, file_info in zip(results, files_to_process):
                status, token, token_index, _, _ = result
                _, _, filename, in_progress_path, _, _, _, _ = file_info

                if status == "cooldown":
                    token_manager.set_token_cooldown(username, token, 30)  # 30 minute cooldown
                    # Move back to failed directory
                    failed_file_path = os.path.join(failed_dir, filename)
                    if os.path.exists(in_progress_path):
                        shutil.move(in_progress_path, failed_file_path)
                else:
                    token_manager.release_token(username, token)

                    if status == "success":
                        # Move to processed directory
                        processed_path = os.path.join(processed_dir, filename)
                        if os.path.exists(in_progress_path):
                            shutil.move(in_progress_path, processed_path)
                            print(f"Moved retry file to processed directory: {processed_path}")
                            logger.info(f"Moved retry file to processed directory: {processed_path}")
                        # Clean up JSONL file if it's different from the original file
                        if in_progress_path.endswith('.jsonl') and filename.endswith('.json'):
                            # This means we converted a JSON to JSONL
                            if os.path.exists(in_progress_path):
                                os.remove(in_progress_path)
                                print(f"Removed temporary JSONL file: {in_progress_path}")
                                logger.info(f"Removed temporary JSONL file: {in_progress_path}")
                        # Remove from failed files list
                        if filename in failed_files:
                            failed_files.remove(filename)
                    else:  # Failed or error
                        # Move back to failed directory
                        failed_file_path = os.path.join(failed_dir, filename)
                        if os.path.exists(in_progress_path):
                            shutil.move(in_progress_path, failed_file_path)
                            print(f"Moved file back to failed directory: {failed_file_path}")
                            logger.info(f"Moved file back to failed directory: {failed_file_path}")
                        # Clean up JSONL file if it's different from the original file
                        if in_progress_path.endswith('.jsonl') and filename.endswith('.json'):
                            # This means we converted a JSON to JSONL
                            if os.path.exists(in_progress_path):
                                os.remove(in_progress_path)
                                print(f"Removed temporary JSONL file after failure: {in_progress_path}")
                                logger.info(f"Removed temporary JSONL file after failure: {in_progress_path}")
                        # Remove from failed files list to avoid infinite retries in this session
                        if filename in failed_files:
                            failed_files.remove(filename)

def process_all_files():
    print(f"Checking base directory: {BASE_DIR}")
    logger.info(f"Checking base directory: {BASE_DIR}")
    if not os.path.exists(BASE_DIR):
        print(f"Base directory does not exist: {BASE_DIR}")
        logger.error(f"Base directory does not exist: {BASE_DIR}")
        return

    user_folders = os.listdir(BASE_DIR)
    print(f"Found user folders: {user_folders}")
    logger.info(f"Found user folders: {user_folders}")

    # Create a queue for user folders
    user_queue = queue.Queue()
    for username in user_folders:
        user_dir = os.path.join(BASE_DIR, username)
        if os.path.isdir(user_dir):
            user_queue.put(username)

    # Process all users and their files
    while not user_queue.empty():
        # Create and start threads
        threads = []
        for _ in range(min(MAX_THREADS, user_queue.qsize())):
            thread = threading.Thread(target=process_user_thread, args=(user_queue,))
            thread.start()
            threads.append(thread)

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

def process_user_thread(user_queue):
    while not user_queue.empty():
        try:
            username = user_queue.get(block=False)
            user_dir = os.path.join(BASE_DIR, username)
            process_user_files(username, user_dir)
        except queue.Empty:
            break
        except Exception as e:
            print(f"Error processing user {username}: {str(e)}")
            logger.error(f"Error processing user {username}: {str(e)}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            logger.error(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    print("Starting Shopify JSON Processor")
    logger.info("Starting Shopify JSON Processor")
    while True:
        try:
            process_all_files()
        except Exception as e:
            print(f"An error occurred during processing: {str(e)}")
            logger.error(f"An error occurred during processing: {str(e)}")

        print(f"Finished processing all files. Waiting for {CHECK_INTERVAL} seconds before next check...")
        logger.info(f"Finished processing all files. Waiting for {CHECK_INTERVAL} seconds before next check...")
        time.sleep(CHECK_INTERVAL)
