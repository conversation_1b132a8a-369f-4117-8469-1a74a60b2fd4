from flask import Blueprint, jsonify, request
from flask_login import login_required
from models.catalog_model import Catalog
from cache_config import cache

filter_bp = Blueprint('manual_inventory_filter', __name__)

@filter_bp.route('/api/games')
@login_required
@cache.cached(timeout=300, key_prefix='games_list')
def get_games():
    # Get unique game names from catalog
    games = list(Catalog.objects().distinct('gameName'))
    print("Games:", games)  # Debug print
    if not games:
        return jsonify([])
    return jsonify({"games": games})

@filter_bp.route('/api/expansions/<game>')
@login_required
@cache.memoize(timeout=300)
def get_expansions(game):
    # Get unique expansions for the selected game
    expansions = list(Catalog.objects(gameName=game).distinct('expansionName'))
    print("Expansions for", game, ":", expansions)  # Debug print
    if not expansions:
        return jsonify([])
    return jsonify({"expansions": expansions})

@filter_bp.route('/api/print-types/<game>/<expansion>')
@login_required
@cache.memoize(timeout=300)
def get_print_types(game, expansion):
    # Get unique print types for the selected game and expansion
    condition = request.args.get('condition', '')
    language = request.args.get('language', '')
    
    print_types = set()
    items = Catalog.objects(gameName=game, expansionName=expansion).only(
        'skus', 'name', 'expansionName', 'gameName'
    )
    for item in items:
        if item.skus:
            for sku in item.skus:
                if (sku.printingName and 
                    (not condition or sku.condAbbr == condition) and
                    (not language or sku.langAbbr == language)):
                    print_types.add(sku.printingName)
    return jsonify({"printTypes": sorted(list(print_types))})

@filter_bp.route('/api/conditions/<game>/<expansion>')
@login_required
@cache.memoize(timeout=300)
def get_conditions(game, expansion):
    # Get unique conditions for the selected game and expansion
    print_type = request.args.get('printType', '')
    conditions = set()
    items = Catalog.objects(gameName=game, expansionName=expansion).only(
        'skus', 'name', 'expansionName', 'gameName'
    )
    for item in items:
        if item.skus:
            for sku in item.skus:
                if sku.condAbbr and (not print_type or sku.printingName == print_type):
                    conditions.add(sku.condAbbr)
    return jsonify({"conditions": sorted(list(conditions))})

@filter_bp.route('/api/languages/<game>/<expansion>')
@login_required
@cache.memoize(timeout=300)
def get_languages(game, expansion):
    # Get unique languages for the selected game and expansion
    print_type = request.args.get('printType', '')
    languages = set()
    items = Catalog.objects(gameName=game, expansionName=expansion).only(
        'skus', 'name', 'expansionName', 'gameName'
    )
    for item in items:
        if item.skus:
            for sku in item.skus:
                if sku.langAbbr and (not print_type or sku.printingName == print_type):
                    languages.add(sku.langAbbr)
    return jsonify({"languages": sorted(list(languages))})

@filter_bp.route('/api/rarities/<game>/<expansion>')
@login_required
@cache.memoize(timeout=300)
def get_rarities(game, expansion):
    # Get unique rarities for the selected game and expansion
    rarities = []
    items = Catalog.objects(gameName=game, expansionName=expansion).only(
        'extendedData', 'name', 'expansionName', 'gameName'
    )
    for item in items:
        rarity = next((data.value for data in item.extendedData if data.name == 'Rarity'), None)
        if rarity and rarity not in rarities:
            rarities.append(rarity)
    return jsonify({"rarities": sorted(rarities)})
