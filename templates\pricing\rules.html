{% extends "base.html" %}

{% block title %}Pricing Rules{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-cogs me-2" style="color: #009688;"></i>
                Pricing Rules
            </h1>
            <p class="text-muted mb-0">Manage automated pricing rules and strategies</p>
        </div>
        <div>
            <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#createRuleModal">
                <i class="fas fa-plus me-1"></i>
                Create Rule
            </button>
            <a href="{{ url_for('pricing.pricing_dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Rules List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Active Pricing Rules</h6>
                </div>
                <div class="card-body">
                    {% if rules %}
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Times Applied</th>
                                    <th>Last Applied</th>
                                    <th>Created By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rule in rules %}
                                <tr>
                                    <td>
                                        <strong>{{ rule.name }}</strong>
                                        {% if rule.description %}
                                        <br><small class="text-muted">{{ rule.description }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ rule.rule_type.replace('_', ' ').title() }}</span>
                                    </td>
                                    <td>
                                        {% if rule.is_active %}
                                        <span class="badge bg-success">Active</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ rule.priority }}</td>
                                    <td>{{ rule.times_applied or 0 }}</td>
                                    <td>
                                        {% if rule.last_applied %}
                                        {{ rule.last_applied.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                        <span class="text-muted">Never</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ rule.created_by }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="editRule('{{ rule.id }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteRule('{{ rule.id }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% if rule.is_active %}
                                            <button class="btn btn-sm btn-outline-warning" onclick="toggleRule('{{ rule.id }}', false)">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                            {% else %}
                                            <button class="btn btn-sm btn-outline-success" onclick="toggleRule('{{ rule.id }}', true)">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-cogs fa-3x text-gray-300 mb-3"></i>
                        <h5 class="text-muted">No Pricing Rules Found</h5>
                        <p class="text-muted">Create your first pricing rule to automate your pricing strategy.</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createRuleModal">
                            <i class="fas fa-plus me-1"></i>
                            Create Your First Rule
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Information Cards -->
    <div class="row">
        <div class="col-md-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Active Rules
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ rules|selectattr('is_active')|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-play fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Applications
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ rules|sum(attribute='times_applied') or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Rule Types
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ rules|map(attribute='rule_type')|unique|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Rule Modal -->
<div class="modal fade" id="createRuleModal" tabindex="-1" aria-labelledby="createRuleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createRuleModalLabel">Create Pricing Rule</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createRuleForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="ruleName" class="form-label">Rule Name</label>
                            <input type="text" class="form-control" id="ruleName" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="ruleType" class="form-label">Rule Type</label>
                            <select class="form-select" id="ruleType" name="rule_type" required>
                                <option value="">Select rule type...</option>
                                <option value="markup_percentage">Markup Percentage</option>
                                <option value="fixed_margin">Fixed Margin</option>
                                <option value="competitive_pricing">Competitive Pricing</option>
                                <option value="bulk_discount">Bulk Discount</option>
                                <option value="category_pricing">Category Pricing</option>
                                <option value="vendor_pricing">Vendor Pricing</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="ruleDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="ruleDescription" name="description" rows="2"></textarea>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="rulePriority" class="form-label">Priority</label>
                            <input type="number" class="form-control" id="rulePriority" name="priority" value="0" min="0" max="100">
                            <div class="form-text">Higher numbers = higher priority</div>
                        </div>
                        <div class="col-md-6">
                            <label for="ruleActive" class="form-label">Status</label>
                            <select class="form-select" id="ruleActive" name="is_active">
                                <option value="true">Active</option>
                                <option value="false">Inactive</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Rule Parameters</label>
                        <div id="ruleParameters">
                            <p class="text-muted">Select a rule type to configure parameters.</p>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Filters</label>
                        <div id="ruleFilters">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="filterVendor" class="form-label">Vendor</label>
                                    <input type="text" class="form-control" id="filterVendor" name="filter_vendor">
                                </div>
                                <div class="col-md-6">
                                    <label for="filterCategory" class="form-label">Category</label>
                                    <input type="text" class="form-control" id="filterCategory" name="filter_category">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createRule()">Create Rule</button>
            </div>
        </div>
    </div>
</div>

<script>
// Handle rule type change to show relevant parameters
document.getElementById('ruleType').addEventListener('change', function() {
    const ruleType = this.value;
    const parametersDiv = document.getElementById('ruleParameters');
    
    let parametersHTML = '';
    
    switch(ruleType) {
        case 'markup_percentage':
            parametersHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <label for="markupPercentage" class="form-label">Markup Percentage</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="markupPercentage" name="markup_percentage" step="0.1" min="0">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                </div>
            `;
            break;
        case 'fixed_margin':
            parametersHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <label for="fixedMargin" class="form-label">Fixed Margin</label>
                        <div class="input-group">
                            <span class="input-group-text">£</span>
                            <input type="number" class="form-control" id="fixedMargin" name="fixed_margin" step="0.01" min="0">
                        </div>
                    </div>
                </div>
            `;
            break;
        case 'competitive_pricing':
            parametersHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <label for="competitorOffset" class="form-label">Competitor Offset</label>
                        <div class="input-group">
                            <span class="input-group-text">£</span>
                            <input type="number" class="form-control" id="competitorOffset" name="competitor_offset" step="0.01">
                        </div>
                        <div class="form-text">Positive = above competitor, negative = below</div>
                    </div>
                </div>
            `;
            break;
        default:
            parametersHTML = '<p class="text-muted">No additional parameters required for this rule type.</p>';
    }
    
    parametersDiv.innerHTML = parametersHTML;
});

function createRule() {
    // Implementation for creating a rule
    alert('Rule creation functionality would be implemented here');
}

function editRule(ruleId) {
    // Implementation for editing a rule
    alert('Edit rule functionality would be implemented here for rule: ' + ruleId);
}

function deleteRule(ruleId) {
    if (confirm('Are you sure you want to delete this pricing rule?')) {
        // Implementation for deleting a rule
        alert('Delete rule functionality would be implemented here for rule: ' + ruleId);
    }
}

function toggleRule(ruleId, activate) {
    const action = activate ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this pricing rule?`)) {
        // Implementation for toggling rule status
        alert(`${action} rule functionality would be implemented here for rule: ` + ruleId);
    }
}
</script>

{% endblock %}
