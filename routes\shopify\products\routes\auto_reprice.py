from flask import Blueprint, jsonify, current_app
from flask_login import login_required, current_user
from pymongo import MongoClient
from bson import ObjectId
import requests
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union
import logging

# Configure logging
logger = logging.getLogger(__name__)

# MongoDB Configuration
mongo_uri = 'mongodb://admin:Reggie2805!@147.93.87.204:27017/?authSource=admin'
mongo_client = MongoClient(mongo_uri)
db = mongo_client['test']
shopify_collection = db['shProducts']
user_collection = db['user']
tcgplayer_key_collection = db['tcgplayerKey']

auto_reprice_bp = Blueprint('auto_reprice', __name__)

class PricingCalculator:
    def __init__(self, settings: dict, currency: str = 'USD'):
        """
        Initialize the pricing calculator with Shopify pricing settings.
        
        Args:
            settings (dict): Shopify pricing settings from ShopifySettings model
            currency (str): Currency code (e.g., 'USD', 'GBP')
        """
        self.settings = settings
        self.currency = currency
        
        # Set defaults for any missing settings
        required_settings = [
            'minPrice', 'price_point', 'price_rounding_enabled', 'price_rounding_thresholds',
            'use_highest_price', 'price_comparison_pairs', 'price_modifiers', 'price_preference_order',
            'game_minimum_prices', 'advancedPricingRules', 'customStepping',
            'tcg_trend_increasing', 'tcg_trend_decreasing'
        ]
        
        defaults = {
            'minPrice': 0.50,
            'price_point': 'Low Price',
            'price_rounding_enabled': False,
            'price_rounding_thresholds': [49, 99],
            'use_highest_price': False,
            'price_comparison_pairs': [],
            'price_modifiers': {},
            'price_preference_order': ['lowPrice', 'marketPrice', 'midPrice', 'highPrice'],
            'game_minimum_prices': {},
            'advancedPricingRules': {},
            'customStepping': {'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50},
            'tcg_trend_increasing': 0.0,
            'tcg_trend_decreasing': 0.0
        }
        
        for setting in required_settings:
            if setting not in self.settings:
                self.settings[setting] = defaults[setting]

    def apply_price_comparison(self, pricing_data: Dict[str, float]) -> Tuple[float, bool]:
        if not pricing_data:
            return None, True
            
        if self.settings.get('use_highest_price', False):
            pairs = self.settings.get('price_comparison_pairs', [])
            price_modifiers = self.settings.get('price_modifiers', {})
            
            highest_price = None
            for pair in pairs:
                price1 = pricing_data.get(pair[0])
                price2 = pricing_data.get(pair[1])
                
                if price1 is not None and price2 is not None:
                    # Calculate prices with modifiers
                    modifier1 = price_modifiers.get(pair[0], 0)
                    modifier2 = price_modifiers.get(pair[1], 0)
                    modified_price1 = round(float(price1) * (1 + modifier1/100), 2)
                    modified_price2 = round(float(price2) * (1 + modifier2/100), 2)
                    
                    # Compare modified prices
                    pair_max = round(max(modified_price1, modified_price2), 2)
                    winner = pair[0] if modified_price1 >= modified_price2 else pair[1]
                    
                    logger.debug(f"Price comparison: {pair[0]}(${price1:.2f} +{modifier1}% = ${modified_price1:.2f}) vs "
                               f"{pair[1]}(${price2:.2f} +{modifier2}% = ${modified_price2:.2f}) → ${pair_max:.2f} ({winner})")
                    
                    if highest_price is None or pair_max > highest_price:
                        highest_price = pair_max
            
            if highest_price is not None:
                return highest_price, False
                
        # Get price preference order from settings
        price_preferences = self.settings.get('price_preference_order', [])
        if not price_preferences:
            # Default order if none specified
            price_preferences = ['lowPrice', 'marketPrice', 'midPrice', 'highPrice']
            
        # Log available prices and apply modifiers
        modified_prices = {}
        price_modifiers = self.settings.get('price_modifiers', {})
        
        logger.debug("Available prices and modifiers:")
        for price_type in ['lowPrice', 'marketPrice', 'midPrice', 'highPrice']:
            if price_type in pricing_data and pricing_data[price_type] is not None:
                base_price = float(pricing_data[price_type])
                modifier = price_modifiers.get(price_type, 0)
                modified_price = round(base_price * (1 + modifier/100), 2)
                modified_prices[price_type] = modified_price
                logger.debug(f"• {price_type}: ${base_price:.2f} +{modifier}% = ${modified_price:.2f}")
        
        # Try each price type in order of preference
        for price_type in price_preferences:
            if price_type in modified_prices:
                price = modified_prices[price_type]
                logger.info(f"Selected price: {price_type} (Preference #{price_preferences.index(price_type) + 1}) = ${price:.2f}")
                return price, False
        
        # If no preferred price types available, try any available price
        for price_type in ['lowPrice', 'marketPrice', 'midPrice', 'highPrice']:
            if price_type in modified_prices:
                price = modified_prices[price_type]
                logger.info(f"Fallback price: {price_type} = ${price:.2f}")
                return price, False
                
        return None, True

    def apply_game_rules(self, price: float, game_name: str, product_type: str, rarity: str = None, variant_title: str = None) -> float:
        if not product_type:
            return price
            
        game_settings = self.settings.get('game_minimum_prices', {}).get(product_type, {})
        if not game_settings:
            return price
            
        # Get print type from title
        variant_lower = variant_title.lower() if variant_title else ''
        print_type = "Normal"  # Default to Normal if no specific print type found
        if 'print_types' in game_settings:
            # Check for specific print types first
            for pt in game_settings['print_types'].keys():
                if pt != "Normal" and pt.lower() in variant_lower:
                    print_type = pt
                    break
                    
        # Apply print type minimum
        print_type_min = game_settings.get('print_types', {}).get(print_type)
        if print_type_min is not None:
            # Print type minimum takes precedence
            price = max(price, print_type_min)
            return price  # Return immediately if print type minimum is applied
            
        # Only check other minimums if no print type minimum was applied
        game_default_min = game_settings.get('default_min_price')
        if game_default_min is not None:
            price = max(price, game_default_min)
                
        if rarity and 'rarities' in game_settings:
            rarity_min = game_settings['rarities'].get(rarity)
            if rarity_min is not None:
                price = max(price, rarity_min)
                    
        return price

    def apply_condition_stepping(self, price: float, condition: str, vendor: str, 
                               product_type: str, expansion: str) -> float:
        key = f"{vendor}_{product_type}_{expansion}"
        stepping_rules = self.settings.get('advancedPricingRules', {}).get(key)
        
        if not stepping_rules:
            stepping_rules = self.settings.get('customStepping', {
                'nm': 100, 'lp': 80, 'mp': 70, 'hp': 65, 'dm': 50
            })

        condition_map = {
            'near mint': 'nm', 'near-mint': 'nm', 'nearmint': 'nm', 'nm': 'nm',
            'lightly played': 'lp', 'lightly-played': 'lp', 'lightlyplayed': 'lp', 'lp': 'lp',
            'moderately played': 'mp', 'moderately-played': 'mp', 'moderatelyplayed': 'mp', 'mp': 'mp',
            'heavily played': 'hp', 'heavily-played': 'hp', 'heavilyplayed': 'hp', 'hp': 'hp',
            'damaged': 'dm', 'dm': 'dm'
        }
            
        condition_code = condition_map.get(condition.lower().strip(), 'nm')
        stepping_percentage = stepping_rules.get(condition_code, 100) / 100
        
        return round(price * stepping_percentage, 2)

    def apply_price_rounding(self, price: float) -> float:
        if not self.settings.get('price_rounding_enabled', False):
            return round(price, 2)
            
        thresholds = sorted(self.settings.get('price_rounding_thresholds', [49, 99]))
        dollars = int(price)
        cents = round((price - dollars) * 100)
        
        for threshold in thresholds:
            if cents <= threshold:
                return dollars + (threshold / 100)
                
        return dollars + 1

    def calculate_final_price(self, sku_info: dict, catalog_item: dict) -> Tuple[float, bool]:
        """
        Calculate the final price in local currency.
        All input prices in sku_info['pricingInfo'] should already be in local currency.
        """
        try:
            # Get base price in local currency using price comparison rules
            base_price, is_missing = self.apply_price_comparison(sku_info.get('pricingInfo', {}))
            if is_missing:
                return None, True

            # Apply TCG trend adjustments if enabled
            if base_price > 0:
                old_price = base_price
                if self.settings.get('tcg_trend_increasing', 0) > 0 and base_price > old_price:
                    increase = self.settings['tcg_trend_increasing'] / 100
                    base_price = round(base_price * (1 + increase), 2)
                elif self.settings.get('tcg_trend_decreasing', 0) > 0 and base_price < old_price:
                    decrease = self.settings['tcg_trend_decreasing'] / 100
                    base_price = round(base_price * (1 - decrease), 2)

            # Apply game rules (min prices are already in local currency)
            nm_price = self.apply_game_rules(
                base_price,
                catalog_item.get('gameName'),
                catalog_item.get('product_type'),
                catalog_item.get('rarity'),
                sku_info.get('title')  # Pass variant title for print type check
            )

            condition = sku_info.get('condName', 'Near Mint').lower().strip()
            
            # Get the NM multiplier from custom stepping
            stepping_rules = self.settings.get('advancedPricingRules', {}).get(
                f"{catalog_item.get('vendor', '')}_{catalog_item.get('product_type', '')}_{catalog_item.get('expansionName', '')}"
            ) or self.settings.get('customStepping', {'nm': 100})
            nm_multiplier = stepping_rules.get('nm', 100) / 100

            # Apply NM multiplier to base price
            nm_price = nm_price * nm_multiplier

            # For NM condition, apply min price check
            if condition in ['near mint', 'near-mint', 'nearmint', 'nm']:
                min_price = float(self.settings.get('minPrice', 0))
                if nm_price < min_price:
                    nm_price = min_price
                price = nm_price
            else:
                # For non-NM conditions:
                # 1. First check if NM would hit min price
                min_price = float(self.settings.get('minPrice', 0))
                if nm_price < min_price:
                    nm_price = min_price
                    
                # 2. Then step down from the adjusted NM price
                price = self.apply_condition_stepping(
                    nm_price,  # Use the min-price-adjusted NM price as base
                    condition,
                    catalog_item.get('vendor', ''),
                    catalog_item.get('product_type', ''),
                    catalog_item.get('expansionName', '')
                )

            # Apply price rounding in local currency only for NM condition
            if condition in ['near mint', 'near-mint', 'nearmint', 'nm']:
                if self.settings.get('price_rounding_enabled', False):
                    price = self.apply_price_rounding(price)
                else:
                    price = round(price, 2)
            else:
                # For non-NM conditions, just apply standard rounding
                price = round(price, 2)
            
            return price, False
            
        except Exception as e:
            logger.error(f"Error calculating price: {str(e)}")
            return None, True

def determine_printing_type(variant_title, valid_subtypes):
    def try_match(title, subtypes_lower):
        if 'foil' in title:
            if 'cold foil' in title and 'cold foil' in subtypes_lower:
                return 'Cold Foil'
            if 'rainbow foil' in title and 'rainbow foil' in subtypes_lower:
                return 'Rainbow Foil'
            if 'reverse holofoil' in title and any('reverse holofoil' in s for s in subtypes_lower):
                return 'Reverse Holofoil'
            if 'holofoil' in subtypes_lower:
                return 'Holofoil'
            if any('foil' in s for s in subtypes_lower):
                for subtype in valid_subtypes:
                    if 'foil' in subtype.lower():
                        return subtype
        return None

    if not valid_subtypes:
        return 'Normal'

    if len(valid_subtypes) == 1:
        return valid_subtypes[0]

    variant_title = variant_title.lower()
    subtypes_lower = {s.lower() for s in valid_subtypes}
    
    match = try_match(variant_title, subtypes_lower)
    if match:
        return match
        
    cleaned_title = variant_title.replace('extended art', '').strip()
    match = try_match(cleaned_title, subtypes_lower)
    if match:
        return match
        
    final_title = cleaned_title.replace('1st edition', '').strip()
    match = try_match(final_title, subtypes_lower)
    if match:
        return match
    
    return 'Normal'

@auto_reprice_bp.route('/api/auto_reprice/<product_id>', methods=['POST'])
@login_required
def auto_reprice_product(product_id):
    try:
        # Get product from MongoDB
        product = shopify_collection.find_one({'_id': ObjectId(product_id), 'username': current_user.username})
        if not product:
            return jsonify({'error': 'Product not found'}), 404

        # Skip if manual override is enabled
        if product.get('manualOverride', False):
            return jsonify({'message': 'Product has manual price override enabled'}), 200

        # Get user profile
        user_profile = user_collection.find_one({'username': current_user.username})
        if not user_profile:
            return jsonify({'error': 'User profile not found'}), 404

        # Get TCGPlayer API key
        tcgplayer_key_doc = tcgplayer_key_collection.find_one({})
        if not tcgplayer_key_doc:
            return jsonify({'error': 'TCGPlayer API key not found'}), 500
        tcgplayer_api_key = tcgplayer_key_doc['latestKey']

        # Initialize pricing calculator
        calculator = PricingCalculator(user_profile, user_profile.get('currency', 'USD'))

        # Get TCGPlayer pricing data
        headers = {
            'Authorization': f'Bearer {tcgplayer_api_key}',
            'Accept': 'application/json',
        }
        
        product_tcg_id = product.get('productId')
        if not product_tcg_id:
            return jsonify({'error': 'Product TCG ID not found'}), 404

        pricing_url = f"https://api.tcgplayer.com/pricing/product/{product_tcg_id}"
        pricing_response = requests.get(pricing_url, headers=headers)
        pricing_response.raise_for_status()
        pricing_data = pricing_response.json().get('results', [])

        if not pricing_data:
            return jsonify({'error': 'No pricing data available'}), 404

        # Get valid subtypes
        valid_subtypes = [p.get('subTypeName') for p in pricing_data if p.get('subTypeName')]

        # Process each variant
        variants = product['variants']
        price_changes = []

        for variant in variants:
            old_price = float(variant['price'])
            printing_type = determine_printing_type(variant['title'], valid_subtypes)
            
            # Find matching price info
            matched_price = next(
                (p for p in pricing_data if p.get('subTypeName', '').lower() == printing_type.lower()),
                next(
                    (p for p in pricing_data if 'holofoil' in p.get('subTypeName', '').lower()),
                    next(
                        (p for p in pricing_data if p.get('subTypeName', '').lower() == 'normal'),
                        pricing_data[0] if pricing_data else None
                    )
                )
            )

            if matched_price:
                # Prepare catalog item info
                catalog_item = {
                    'gameName': product.get('product_type'),
                    'product_type': product.get('product_type'),
                    'rarity': product.get('rarity'),
                    'vendor': product.get('vendor'),
                    'expansionName': product.get('expansionName')
                }

                # Extract and format pricing info
                pricing_info = {}
                
                # Get market price first as it's often the most reliable
                market_price = matched_price.get('marketPrice')
                if market_price is not None:
                    pricing_info['marketPrice'] = float(market_price)
                    logger.info(f"Market Price: ${market_price}")
                
                # Get low price (try directLowPrice first, then lowPrice)
                low_price = matched_price.get('directLowPrice')
                if low_price is None:
                    low_price = matched_price.get('lowPrice')
                if low_price is not None:
                    pricing_info['lowPrice'] = float(low_price)
                    logger.info(f"Low Price: ${low_price}")
                
                # Get mid price (fallback to market price if not available)
                mid_price = matched_price.get('midPrice')
                if mid_price is None and market_price is not None:
                    mid_price = market_price
                if mid_price is not None:
                    pricing_info['midPrice'] = float(mid_price)
                    logger.info(f"Mid Price: ${mid_price}")
                
                # Get high price (fallback to market price if not available)
                high_price = matched_price.get('highPrice')
                if high_price is None and market_price is not None:
                    high_price = market_price
                if high_price is not None:
                    pricing_info['highPrice'] = float(high_price)
                    logger.info(f"High Price: ${high_price}")
                
                # Log summary of extracted prices
                logger.info(f"\nExtracted prices for {printing_type}:")
                for price_type, price in pricing_info.items():
                    logger.info(f"  {price_type}: ${price}")

                # Prepare SKU info with formatted pricing data
                sku_info = {
                    'pricingInfo': pricing_info,
                    'condName': variant.get('option1', 'Near Mint'),
                    'title': variant['title']
                }

                # Calculate new price
                new_price, is_missing = calculator.calculate_final_price(sku_info, catalog_item)

                if not is_missing and new_price is not None:
                    if abs(old_price - new_price) > 0.01:
                        price_changes.append({
                            'variant_title': variant['title'],
                            'old_price': old_price,
                            'new_price': new_price
                        })

        if price_changes:
            return jsonify({
                'message': 'Price changes calculated',
                'price_changes': price_changes
            })
        else:
            return jsonify({
                'message': 'No price changes needed',
                'price_changes': []
            })

    except requests.RequestException as e:
        logger.error(f"Failed to get TCGPlayer prices: {str(e)}")
        return jsonify({'error': f'Failed to get TCGPlayer prices: {str(e)}'}), 500
    except Exception as e:
        logger.error(f"Error during auto-repricing: {str(e)}")
        return jsonify({'error': f'An error occurred during auto-repricing: {str(e)}'}), 500
