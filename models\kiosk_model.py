from mongoengine import Document, <PERSON>Field, BooleanField, DateTimeField
from datetime import datetime

class KioskSession(Document):
    """Model for tracking kiosk sessions"""
    till_id = StringField(required=True)
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.utcnow)
    last_activity = DateTimeField(default=datetime.utcnow)
    staff_mode = BooleanField(default=False)
    
    meta = {
        'collection': 'kiosk_sessions',
        'indexes': [
            {'fields': ['till_id']},
            {'fields': ['created_at']},
            {'fields': ['is_active']}
        ]
    }
