from mongoengine import Document, StringField, FloatField, BooleanField, DateTimeField
from datetime import datetime

class CustomBuylist(Document):
    """
    Model for custom buylist items that users can create for bulk purchases.
    These are items like bulk cards, collections, etc. that have fixed prices.
    """
    title = StringField(required=True)
    description = StringField(required=True)
    username = StringField(required=True)  # Store the username of the creator
    cash_price = FloatField(required=True)  # Fixed cash price
    credit_price = FloatField(required=True)  # Fixed credit price
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    is_active = BooleanField(default=True)
    
    meta = {
        'collection': 'customBuylist',
        'indexes': [
            'username',
            'is_active'
        ]
    }
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.utcnow()
        return super(CustomBuylist, self).save(*args, **kwargs)
