from flask import Blueprint

# Create the Blueprint at the module level
buylist_bp = Blueprint('buylist', __name__, template_folder='templates')

# Import all route modules to register them with the blueprint
from routes.buylist.core import *
from routes.buylist.routes import *
from routes.buylist.price_fetcher import *
from routes.buylist.order_management import *
from routes.buylist.email_service import *
from routes.buylist.inventory_service import *

# Function to get the blueprint
def create_buylist_bp():
    return buylist_bp
