import logging
from flask import render_template, request, jsonify
from flask_login import login_required, current_user
from bson import ObjectId
from datetime import datetime

from ..config import shopify_collection, user_collection
from ..utils import (
    get_shopify_headers,
    get_shopify_store_url,
    create_shopify_session,
    update_product_variants
)
from utils.cache_utils import get_cached_value, set_cached_value

logger = logging.getLogger(__name__)

@login_required
def products():
    """Render the products page"""
    return render_template('shopify_products.html')

@login_required
def get_vendors():
    """Get list of vendors for filtering"""
    item_type = request.args.get('itemType')
    username = current_user.username

    # Try to get from cache first
    cache_key = f"vendors:{username}:{item_type if item_type else 'all'}"
    cached_vendors = get_cached_value(cache_key)
    if cached_vendors is not None:
        return jsonify({'vendors': cached_vendors})

    # If not in cache, query database
    query = {'username': username}
    if item_type == 'tcg':
        query['tcgItem'] = True

    pipeline = [
        {'$match': query},
        {'$group': {'_id': '$vendor'}},
        {'$project': {'_id': 1}},
        {'$sort': {'_id': 1}},
        {'$limit': 1000},  # Reasonable limit
        {'$hint': 'idx_username_tcgitem_vendor'}  # Force index usage
    ]
    
    try:
        # Execute aggregation with timeout and memory limit
        vendors = [
            doc['_id'] for doc in shopify_collection.aggregate(
                pipeline,
                allowDiskUse=False,  # Prevent memory overload
                maxTimeMS=5000,  # 5 second timeout
                hint='idx_username_tcgitem_vendor'
            ) if doc['_id']
        ]
        
        # Cache the results
        set_cached_value(cache_key, vendors, ttl_seconds=300)  # Cache for 5 minutes
        
        return jsonify({'vendors': vendors})
    except Exception as e:
        logger.error(f"Error in get_vendors: {str(e)}")
        return jsonify({'error': 'An error occurred while fetching vendors'}), 500

@login_required
def get_product_types():
    """Get list of product types for filtering"""
    vendor = request.args.get('vendor')
    username = current_user.username

    # Try to get from cache first
    cache_key = f"product_types:{username}:{vendor if vendor else 'all'}"
    cached_types = get_cached_value(cache_key)
    if cached_types is not None:
        return jsonify({'product_types': cached_types})

    # If not in cache, query database
    query = {'username': username}
    if vendor:
        query['vendor'] = vendor

    pipeline = [
        {'$match': query},
        {'$group': {'_id': '$product_type'}},
        {'$project': {'_id': 1}},
        {'$sort': {'_id': 1}},
        {'$limit': 1000},  # Reasonable limit
        {'$hint': 'idx_username_vendor_product_type'}  # Force index usage
    ]
    
    try:
        # Execute aggregation with timeout and memory limit
        product_types = [
            doc['_id'] for doc in shopify_collection.aggregate(
                pipeline,
                allowDiskUse=False,  # Prevent memory overload
                maxTimeMS=5000,  # 5 second timeout
                hint='idx_username_vendor_product_type'
            ) if doc['_id']
        ]
        
        # Cache the results
        set_cached_value(cache_key, product_types, ttl_seconds=300)  # Cache for 5 minutes
        
        return jsonify({'product_types': product_types})
    except Exception as e:
        logger.error(f"Error in get_product_types: {str(e)}")
        return jsonify({'error': 'An error occurred while fetching product types'}), 500

@login_required
def get_expansion_names():
    """Get list of expansion names for filtering"""
    vendor = request.args.get('vendor')
    product_type = request.args.get('productType')
    if not vendor or not product_type:
        return jsonify({'error': 'Vendor and Product Type are required'}), 400

    query = {
        'vendor': vendor,
        'product_type': product_type,
        'username': current_user.username
    }

    pipeline = [
        {'$match': query},
        {'$group': {'_id': '$expansionName'}},
        {'$sort': {'_id': 1}}
    ]
    
    expansion_names = [doc['_id'] for doc in shopify_collection.aggregate(pipeline)]
    return jsonify(expansion_names)

@login_required
def get_products():
    """Get filtered list of products"""
    item_type = request.args.get('itemType')
    vendor = request.args.get('vendor')
    product_type = request.args.get('productType')
    expansion_name = request.args.get('expansionName')
    search_term = request.args.get('searchTerm', '')
    in_stock_only = request.args.get('inStockOnly', 'false').lower() == 'true'
    needs_pushing = request.args.get('needsPushing', 'false').lower() == 'true'
    sold_only = request.args.get('soldOnly', 'false').lower() == 'true'
    page = int(request.args.get('page', 1))
    per_page = 25

    match_stage = {'username': current_user.username}
    if search_term:
        match_stage['title'] = {'$regex': search_term, '$options': 'i'}
    if item_type == 'tcg':
        match_stage['tcgItem'] = True
    if vendor:
        match_stage['vendor'] = vendor
    if product_type:
        match_stage['product_type'] = product_type
    if expansion_name:
        match_stage['expansionName'] = expansion_name
    if in_stock_only:
        match_stage['variants.inventory_quantity'] = {'$gt': 0}
    if sold_only:
        match_stage['last_sold_date'] = {'$exists': True}
    if needs_pushing:
        match_stage['needsPushing'] = True
    manual_attention = request.args.get('manualAttention', 'false').lower() == 'true'
    if manual_attention:
        match_stage['needsManual'] = True

    skip = (page - 1) * per_page

    # Base pipeline for filtering and computing fields
    base_pipeline = [
        {'$match': match_stage},
        {'$lookup': {
            'from': 'shProducts',
            'let': {'productId': {'$toInt': '$productId'}, 'username': '$username'},
            'pipeline': [
                {'$match': {
                    '$expr': {
                        '$and': [
                            {'$eq': ['$productId', '$$productId']},
                            {'$eq': ['$username', '$$username']}
                        ]
                    }
                }},
                {'$project': {'variants.title': 1}}
            ],
            'as': 'shProduct'
        }},
        {'$project': {
            'shopifyId': '$id',
            'title': 1,
            'image': {'$ifNull': ['$image.src', None]},
            'expansionName': 1,
            'variants': 1,
            'last_sold_date': 1,
            'shProductVariants': {'$arrayElemAt': ['$shProduct.variants', 0]},
            'manualOverride': 1
        }},
        {'$addFields': {
            'lowest_price': {'$min': '$variants.price'},
            'highest_price': {'$max': '$variants.price'},
            'total_quantity': {'$sum': '$variants.inventory_quantity'},
            'average_sell_price': {'$avg': '$variants.price'}
        }}
    ]

    # Add sorting if specified
    sort_price = request.args.get('sortPrice')
    if sort_price == 'low-to-high':
        base_pipeline.append({'$sort': {'lowest_price': 1, 'title': 1}})
    elif sort_price == 'high-to-low':
        base_pipeline.append({'$sort': {'highest_price': -1, 'title': 1}})
    else:
        base_pipeline.append({'$sort': {'title': 1}})

    # Final pipeline with pagination
    pipeline = base_pipeline + [
        {'$facet': {
            'paginatedResults': [{'$skip': skip}, {'$limit': per_page}],
            'totalCount': [{'$count': 'count'}]
        }}
    ]

    result = list(shopify_collection.aggregate(pipeline))

    if result and result[0]['paginatedResults']:
        products = result[0]['paginatedResults']
        total_count = result[0]['totalCount'][0]['count'] if result[0]['totalCount'] else 0

        for product in products:
            if '_id' in product:
                product['_id'] = str(product['_id'])

        return jsonify({
            'products': products,
            'total': total_count,
            'page': page,
            'per_page': per_page
        })
    else:
        return jsonify({
            'products': [],
            'total': 0,
            'page': page,
            'per_page': per_page
        })

@login_required
def get_product(product_id):
    """Get detailed product information"""
    product = shopify_collection.find_one({
        '_id': ObjectId(product_id),
        'username': current_user.username
    })
    if not product:
        return jsonify({'error': 'Product not found'}), 404

    # Get user's Shopify store URL
    user_profile = user_collection.find_one({'username': current_user.username})
    if user_profile:
        product['shopify_store'] = user_profile.get('shopifyStoreName', '') + '.myshopify.com'

    if '_id' in product:
        product['_id'] = str(product['_id'])
    
    # Format last_repriced date if it exists
    if 'last_repriced' in product and product['last_repriced']:
        try:
            # Handle MongoDB $date format
            if isinstance(product['last_repriced'], dict) and '$date' in product['last_repriced']:
                last_repriced = product['last_repriced']['$date']
            else:
                last_repriced = str(product['last_repriced'])
                    
            # Convert to UK formatted datetime
            dt = datetime.fromisoformat(last_repriced.replace('Z', '+00:00'))
            product['last_repriced'] = dt.strftime('%d/%m/%Y %H:%M:%S (UK)')
        except Exception as e:
            logger.error(f"Error formatting last_repriced date: {e}")
            product['last_repriced'] = None
    else:
        product['last_repriced'] = None

    return jsonify(product)

@login_required
def delete_product():
    """Delete a product from both Shopify and local database"""
    data = request.json
    product_id = data.get('productId')
    
    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400

    try:
        # Get product from database
        product = shopify_collection.find_one({
            '_id': ObjectId(product_id),
            'username': current_user.username
        })
        if not product:
            return jsonify({'error': 'Product not found'}), 404

        shopify_product_id = product.get('id')
        if not shopify_product_id:
            return jsonify({'error': 'Shopify product ID not found'}), 404

        # Delete from Shopify first
        headers = get_shopify_headers(current_user.username)
        store_url = get_shopify_store_url(current_user.username)
        session = create_shopify_session()
        
        url = f"{store_url}/admin/api/2023-10/products/{shopify_product_id}.json"
        response = session.delete(url, headers=headers)
        
        # Handle different response scenarios
        if response.status_code == 200:
            shopify_response = f"Successfully deleted product {shopify_product_id} from Shopify"
            logger.info(f"User {current_user.username} deleted product {shopify_product_id} from Shopify")
        elif response.status_code == 404:
            # Product already deleted from Shopify or doesn't exist
            shopify_response = f"Product {shopify_product_id} not found on Shopify (may have been previously deleted)"
            logger.info(f"Product {shopify_product_id} not found on Shopify when user {current_user.username} attempted to delete it")
        else:
            # Handle other error responses
            response.raise_for_status()  # This will raise an exception for other error codes
        
        # Then delete from local database
        result = shopify_collection.delete_one({
            '_id': ObjectId(product_id),
            'username': current_user.username
        })
        
        if result.deleted_count == 0:
            logger.warning(f"Product was deleted from Shopify but not found in local database: {product_id}")
            return jsonify({
                'message': 'Product deleted from Shopify but not found in local database',
                'shopify_response': shopify_response
            }), 200
        
        return jsonify({
            'message': 'Product deleted successfully from Shopify and local database',
            'shopify_response': shopify_response
        }), 200
        
    except Exception as e:
        logger.error(f"Failed to delete product: {str(e)}")
        return jsonify({
            'error': f'Failed to delete product: {str(e)}',
            'shopify_response': 'Error occurred during deletion process'
        }), 500

@login_required
def update_manual_override():
    """Update manual override setting for a product"""
    data = request.json
    product_id = data.get('productId')
    manual_override = data.get('manualOverride')
    
    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400

    try:
        result = shopify_collection.update_one(
            {'_id': ObjectId(product_id), 'username': current_user.username},
            {'$set': {'manualOverride': manual_override}}
        )
        
        if result.modified_count > 0:
            return jsonify({'message': 'Manual override setting updated successfully'}), 200
        else:
            return jsonify({'error': 'Product not found or no changes made'}), 404
            
    except Exception as e:
        logger.error(f"Error updating manual override: {str(e)}")
        return jsonify({'error': f'An error occurred: {str(e)}'}), 500
