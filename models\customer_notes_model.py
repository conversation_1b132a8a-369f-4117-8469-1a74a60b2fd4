from datetime import datetime
from bson import ObjectId

class CustomerNote:
    def __init__(self):
        self.collection_name = 'customer_notes'

    def create_note(self, db, customer_id: str, text: str, username: str, staff_name: str):
        note = {
            "customer_id": customer_id,
            "sections": [{
                "text": text,
                "created_at": datetime.utcnow(),
                "username": username,
                "staff_name": staff_name
            }],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        result = db[self.collection_name].insert_one(note)
        return str(result.inserted_id)

    def add_note_section(self, db, note_id: str, text: str, username: str, staff_name: str):
        now = datetime.utcnow()
        result = db[self.collection_name].update_one(
            {"_id": ObjectId(note_id)},
            {
                "$push": {
                    "sections": {
                        "text": text,
                        "created_at": now,
                        "username": username,
                        "staff_name": staff_name
                    }
                },
                "$set": {"updated_at": now}
            }
        )
        return result.modified_count > 0

    def get_customer_notes(self, db, customer_id: str, username: str = None):
        query = {"customer_id": customer_id}
        if username:
            query["username"] = username
            
        cursor = db[self.collection_name].find(query).sort("updated_at", -1)
        notes = []
        for note in cursor:
            note["_id"] = str(note["_id"])
            notes.append(note)
        return notes
        
    def delete_customer_notes(self, db, customer_id: str):
        """
        Delete all notes associated with a customer
        
        Args:
            db: MongoDB database connection
            customer_id: ID of the customer whose notes should be deleted
            
        Returns:
            Number of notes deleted
        """
        result = db[self.collection_name].delete_many({"customer_id": customer_id})
        return result.deleted_count

    def create_indexes(self, db):
        db[self.collection_name].create_index([("customer_id", 1), ("username", 1)])
        db[self.collection_name].create_index([("created_at", -1)])
