from datetime import datetime
from mongoengine import Document, StringField, ListField, FloatField, DateTimeField, DictField

class EUPurchase(Document):
    username = StringField(required=True)
    items = ListField(DictField(), required=True)  # List of dicts with idProduct, name, quantity, price_eur, isFoil
    total_eur = FloatField(required=True)
    total_aud = FloatField(required=True)
    status = StringField(default="pending")
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'euPurchase',
        'indexes': [
            'username',
            'status',
            'created_at'
        ]
    }

    def to_dict(self):
        return {
            "id": str(self.id),
            "username": self.username,
            "items": self.items,
            "total_eur": self.total_eur,
            "total_aud": self.total_aud,
            "status": self.status,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @staticmethod
    def from_dict(data):
        purchase = EUPurchase(
            username=data["username"],
            items=data["items"],
            total_eur=data["total_eur"],
            total_aud=data["total_aud"],
            status=data.get("status", "pending")
        )
        purchase._id = data["_id"]
        purchase.created_at = data["created_at"]
        purchase.updated_at = data["updated_at"]
        return purchase
