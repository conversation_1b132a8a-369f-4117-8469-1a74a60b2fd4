from datetime import datetime, timedelta
from mongoengine import Document, StringField, FloatField, DateTimeField, ListField, DictField, BooleanField

class KioskOrder(Document):
    customer_name = StringField(required=True)
    username = <PERSON><PERSON>ield(required=True)  # Store the logged in username
    items = ListField(DictField(), required=True)  # Store item details as dictionaries
    total = FloatField(required=True)
    created_at = DateTimeField(default=datetime.utcnow)
    expires_at = DateTimeField()
    status = StringField(default='pending', choices=['pending', 'processing', 'completed', 'cancelled'])

    meta = {
        'collection': 'kioskOrders',  # Explicitly set collection name
        'indexes': [
            'customer_name',
            'created_at',
            'status',
            'username'
        ]
    }

    def save(self, *args, **kwargs):
        if not self.expires_at:
            self.expires_at = self.created_at + timedelta(minutes=60)
        return super(<PERSON>oskOrde<PERSON>, self).save(*args, **kwargs)

    @classmethod
    def update_status(cls, order_id, new_status):
        """Update order status"""
        return cls.objects(id=order_id).update_one(set__status=new_status)
