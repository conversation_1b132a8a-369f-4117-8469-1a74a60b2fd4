from flask import render_template, request, jsonify, abort, redirect, url_for, session, flash, make_response
from flask_login import login_required, current_user
from itsdangerous import URLSafeTimedSerializer
from bson import ObjectId, json_util
import re
import logging
from datetime import datetime
import csv
import io
from models.user_settings_model import UserSettings
from routes.buylist import buylist_bp
from routes.buylist.core import logger, order_collection, catalog_collection, user_collection
from pymongo import UpdateOne
from datetime import timedelta

# Initialize hotlist collection
from routes.buylist.core import db
hotlist_collection = db.hotlist

# Cache for expansion names by productId
expansion_name_cache = {}

@buylist_bp.route('/')
@login_required
def buylist_dashboard():
    return render_template('buylist_dashboard.html')

@buylist_bp.route('/create')
@login_required
def view_buylist():
    checkout_items = session.get('checkout_items', [])
    total_cash = sum(item['offeredPrice'] for item in checkout_items if item['type'] == 'cash')
    total_credit = sum(item['offeredPrice'] for item in checkout_items if item['type'] == 'credit')
    return render_template('buylist.html', checkout_items=checkout_items, total_cash=total_cash, total_credit=total_credit)

@buylist_bp.route('/orders')
@login_required
def buylist_orders():
    orders = order_collection.find(
        {'username': current_user.username},
        {'_id': 1, 'date': 1, 'customer_name': 1, 'total': 1, 'orderStatus': 1}
    ).sort('date', -1)
    return render_template('buylist_orders.html', orders=orders)

@buylist_bp.route('/mark_order_sent/<order_id>', methods=['POST'])
@login_required
def mark_order_sent(order_id):
    try:
        # Update the order status to "sent"
        result = order_collection.update_one(
            {'_id': ObjectId(order_id)},
            {'$set': {'orderStatus': 'Sent', 'sent_date': datetime.utcnow()}}
        )

        if result.modified_count > 0:
            logger.info(f"Order {order_id} marked as sent successfully")
            return jsonify({'success': True, 'message': 'Order marked as sent successfully'})
        else:
            logger.error(f"Order {order_id} not found or already marked as sent")
            return jsonify({'success': False, 'error': 'Order not found or already marked as sent'}), 404

    except Exception as e:
        logger.error(f"Error marking order {order_id} as sent: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500

@buylist_bp.route('/api/get-orders')
@login_required
def get_orders():
    username = current_user.username
    logger.info(f"Fetching orders for user: {username}")

    try:
        query = {'username': username}
        logger.info(f"Query: {query}")

        pipeline = [
            {'$match': query},
            {'$sort': {'date': -1}},
            {'$project': {
                'date': 1,
                'customer_name': 1,
                'customer_email': 1,
                'total': 1,
                'orderStatus': 1,
                'email': 1,
                'staff_name': 1,
                'line_items': {
                    '$map': {
                        'input': '$line_items',
                        'as': 'item',
                        'in': {
                            'skuId': {'$ifNull': ['$$item.skuId', None]},
                            'name': '$$item.name',
                            'quantity': '$$item.quantity',
                            'offeredPrice': '$$item.offeredPrice',
                            'type': '$$item.type',
                            'productId': '$$item.productId'
                        }
                    }
                },
                'cost_of_goods': {
                    '$reduce': {
                        'input': '$line_items',
                        'initialValue': 0,
                        'in': {
                            '$add': [
                                '$$value',
                                {'$multiply': ['$$this.quantity', '$$this.offeredPrice']}
                            ]
                        }
                    }
                },
                'current_value': {
                    '$reduce': {
                        'input': '$line_items',
                        'initialValue': 0,
                        'in': {
                            '$add': [
                                '$$value',
                                {'$multiply': ['$$this.quantity', {'$ifNull': ['$$this.currentMarketPrice', 0]}]}
                            ]
                        }
                    }
                }
            }}
        ]

        logger.info(f"Executing aggregation pipeline: {pipeline}")
        orders = list(order_collection.aggregate(pipeline))
        logger.info(f"Found {len(orders)} orders for user {username}")

        # Fetch catalog items and current prices in bulk
        sku_ids = set(item['skuId'] for order in orders for item in order['line_items'] if item['skuId'] is not None)
        # Get catalog items with card numbers
        catalog_items = {}
        # First get items by skuId
        sku_items = catalog_collection.find(
            {'skus.skuId': {'$in': list(sku_ids)}},
            {'skus.$': 1, 'image': 1, 'productId': 1, 'expansionName': 1, 'url': 1, 'number': 1}
        )
        for item in sku_items:
            if item.get('skus'):
                catalog_items[item['skus'][0]['skuId']] = {
                    'image': item.get('image'),
                    'productId': item.get('productId'),
                    'expansionName': item.get('expansionName'),
                    'url': item.get('url'),
                    'marketPrice': item['skus'][0].get('marketPrice', 0) if item.get('skus') else 0,
                    'number': item.get('number', 'N/A')  # Add card number
                }

        # Then get items by productId for items without skuId
        product_ids = [int(item['productId']) for item in
                      [item for order in orders for item in order['line_items']]
                      if 'productId' in item and item['productId'] is not None and item['skuId'] is None]
        if product_ids:
            product_items = catalog_collection.find(
                {'productId': {'$in': product_ids}},
                {'productId': 1, 'image': 1, 'expansionName': 1, 'url': 1, 'number': 1}
            )
            for item in product_items:
                catalog_items[f"p{item['productId']}"] = {
                    'image': item.get('image'),
                    'productId': item.get('productId'),
                    'expansionName': item.get('expansionName'),
                    'url': item.get('url'),
                    'marketPrice': 0,
                    'number': item.get('number', 'N/A')  # Add card number
                }

        # Update orders with current market prices from TCGPlayer API
        bulk_updates = []

        # Collect all SKU IDs from all orders for batch processing
        all_sku_ids = []
        for order in orders:
            for item in order['line_items']:
                if item['skuId'] is not None:
                    all_sku_ids.append(item['skuId'])

        # Import the price fetcher function
        from routes.buylist.price_fetcher import fetch_tcgplayer_prices_direct

        # Fetch prices for all SKUs in parallel
        logger.info(f"Fetching prices for {len(all_sku_ids)} SKUs across all orders")
        all_price_data = fetch_tcgplayer_prices_direct(all_sku_ids)
        logger.info(f"Successfully fetched prices for {len(all_price_data)} SKUs")

        # Ensure all price data has numeric values (not null)
        for sku_id, price_data in all_price_data.items():
            if price_data:
                # Convert any null values to 0
                price_data['price'] = float(price_data.get('price', 0) or 0)
                price_data['marketPrice'] = float(price_data.get('marketPrice', 0) or 0)
                price_data['lowPrice'] = float(price_data.get('lowPrice', 0) or 0)
                price_data['midPrice'] = float(price_data.get('midPrice', 0) or 0)
                price_data['highPrice'] = float(price_data.get('highPrice', 0) or 0)

                # Log the normalized price data
                logger.info(f"Normalized price data for SKU {sku_id}: lowPrice=${price_data['lowPrice']}, marketPrice=${price_data['marketPrice']}")

        # Update each order with the fetched prices
        for order in orders:
            order_total_value = 0

            # Update each line item with current market price
            for item in order['line_items']:
                # Ensure we have a valid skuId
                if item['skuId'] is None:
                    item['currentMarketPrice'] = 0
                    logger.warning(f"Skipping item with no SKU ID: {item.get('name', 'Unknown')}")
                    continue

                # Try to get price from API response
                if str(item['skuId']) in all_price_data:
                    current_price_info = all_price_data[str(item['skuId'])]
                    # Ensure we have a valid lowPrice, defaulting to 0 if not
                    current_market_price = float(current_price_info.get('lowPrice', 0) or 0)
                    item['currentMarketPrice'] = current_market_price
                    order_total_value += current_market_price * int(item['quantity'])
                    logger.info(f"Using API price for SKU {item['skuId']}: ${current_market_price}")
                else:
                    # Fallback to catalog price if TCGPlayer price not available
                    catalog_item = catalog_items.get(item['skuId'])
                    if catalog_item:
                        current_market_price = float(catalog_item.get('marketPrice', 0) or 0)
                        item['currentMarketPrice'] = current_market_price
                        order_total_value += current_market_price * int(item['quantity'])
                        logger.info(f"Using catalog price for SKU {item['skuId']}: ${current_market_price}")
                    else:
                        item['currentMarketPrice'] = 0
                        logger.warning(f"No price data available for SKU {item['skuId']}")

            bulk_updates.append(
                UpdateOne(
                    {'_id': order['_id']},
                    {
                        '$set': {
                            'current_value': order_total_value,
                            'line_items': order['line_items']
                        }
                    }
                )
            )

        # Enrich orders with catalog data
        for order in orders:
            for item in order['line_items']:
                if item['skuId'] is not None:
                    catalog_item = catalog_items.get(item['skuId'])
                    if catalog_item:
                        item['image'] = catalog_item.get('image')
                        item['productId'] = catalog_item.get('productId')
                        item['expansionName'] = catalog_item.get('expansionName')
                        item['number'] = catalog_item.get('number', 'N/A')  # Add card number
                    item['url'] = str(catalog_item.get('url', '')) if catalog_item else ''

        # Convert ObjectId to string for JSON serialization
        for order in orders:
            if '_id' in order:
                order['_id'] = str(order['_id'])

        logger.info(f"Serialized orders. Number of orders: {len(orders)}")

        return jsonify(orders), 200
    except Exception as e:
        logger.error(f"Error fetching orders for user {username}: {str(e)}", exc_info=True)
        return jsonify({'error': 'An error occurred while fetching orders'}), 500

@buylist_bp.route('/api/get-tcgplayer-price', methods=['POST'])
@login_required
def get_tcgplayer_price():
    """
    API endpoint to fetch TCGPlayer prices for multiple SKUs.

    Expects a JSON payload with a 'skuIds' array.
    Returns a dictionary mapping SKU IDs to price data.
    """
    try:
        data = request.json
        if not data or 'skuIds' not in data:
            logger.error("Missing skuIds in request")
            return jsonify({'error': 'Missing skuIds in request'}), 400

        sku_ids = data['skuIds']
        quantities = data.get('quantities', [])
        logger.info(f"Fetching TCGPlayer prices for {len(sku_ids)} SKUs")

        # Import the price fetcher function
        from routes.buylist.price_fetcher import fetch_tcgplayer_prices_direct

        # Fetch prices for all SKUs
        price_data = fetch_tcgplayer_prices_direct(sku_ids)
        logger.info(f"Successfully fetched prices for {len(price_data)} SKUs")

        # Ensure all price data has numeric values and calculate total values with quantities
        for sku_id, price_info in price_data.items():
            if price_info:
                # Convert any null values to 0
                price_info['price'] = float(price_info.get('price', 0) or 0)
                price_info['marketPrice'] = float(price_info.get('marketPrice', 0) or 0)
                price_info['lowPrice'] = float(price_info.get('lowPrice', 0) or 0)
                price_info['midPrice'] = float(price_info.get('midPrice', 0) or 0)
                price_info['highPrice'] = float(price_info.get('highPrice', 0) or 0)

                # Find the quantity for this SKU if available
                try:
                    idx = sku_ids.index(sku_id)
                    quantity = int(quantities[idx]) if idx < len(quantities) else 1
                except (ValueError, IndexError):
                    quantity = 1

                # Add total value with quantity
                price_info['totalValue'] = price_info['lowPrice'] * quantity
                logger.info(f"SKU {sku_id}: Price ${price_info['lowPrice']} × Quantity {quantity} = ${price_info['totalValue']}")

        return jsonify(price_data)
    except Exception as e:
        logger.error(f"Error fetching TCGPlayer prices: {str(e)}", exc_info=True)
        return jsonify({'error': f'An error occurred: {str(e)}'}), 500

@buylist_bp.route('/api/user-profile')
@login_required
def get_user_profile():
    try:
        user = user_collection.find_one({'username': current_user.username})
        if user:
            # Remove sensitive information
            user.pop('password', None)
            # Convert datetime fields to ISO format strings
            for key, value in user.items():
                if isinstance(value, datetime):
                    user[key] = value.isoformat()
            return jsonify(user)
        else:
            return jsonify({'error': 'User not found'}), 404
    except Exception as e:
        logger.error(f"Error fetching user profile: {str(e)}", exc_info=True)
        return jsonify({'error': 'An error occurred while fetching user profile'}), 500

@buylist_bp.route('/process-order/<order_id>')
@buylist_bp.route('/cardmarket/process-order/<order_id>')
@buylist_bp.route('/cardmarket/buylist/process-order/<order_id>')
@buylist_bp.route('/cardmarket/buylist/cardmarket/process-order/<order_id>')
@buylist_bp.route('/cardmarket/buylist/cardmarket/buylist/process-order/<order_id>')
@login_required
def process_order(order_id):
    from routes.buylist.order_management import calculate_cost_of_goods

    response = make_response()
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    logger.info(f"Processing order: {order_id}")

    page = request.args.get('page', 1, type=int)
    per_page = 50  # Number of line items per page

    try:
        if not isinstance(order_id, ObjectId):
            order_id = ObjectId(order_id)

        order = order_collection.find_one(
            {'_id': order_id},
            {
                'customer_email': 1,
                'line_items': 1,  # Get all line items
                'date': 1,
                'customer_name': 1,
                'total': 1,
                'orderStatus': 1
            }
        )

        if order:
            logger.info(f"Order found: {order}")
            total_line_items = len(order.get('line_items', []))
            total_pages = (total_line_items + per_page - 1) // per_page

            # Calculate total cash and credit from line items
            total_cash = sum(item['offeredPrice'] * item['quantity'] for item in order.get('line_items', []) if item.get('type', '').lower() == 'cash')
            total_credit = sum(item['offeredPrice'] * item['quantity'] for item in order.get('line_items', []) if item.get('type', '').lower() == 'credit')
            order['total_cash'] = total_cash
            order['total_credit'] = total_credit

            if page == 1:
                original_order = order.copy()
                order['original_order'] = original_order

                if order.get('customer_email'):
                    other_orders = order_collection.count_documents({
                        'customer_email': order['customer_email'],
                        '_id': {'$ne': order_id}
                    })
                    order['customer_order_count'] = other_orders
                else:
                    order['customer_order_count'] = 0

            cost_of_goods = calculate_cost_of_goods(current_user.username)

            sku_ids = [item['skuId'] for item in order.get('line_items', []) if 'skuId' in item]
            # Get catalog items with card numbers
            catalog_items = list(catalog_collection.find(
                {'skus.skuId': {'$in': sku_ids}},
                {
                    'image': 1,
                    'skus': {'$elemMatch': {'skuId': {'$in': sku_ids}}},
                    'name': 1,
                    'productId': 1,
                    'expansionName': 1,
                    'url': 1,
                    'rarity': 1,
                    'number': 1  # Add card number field
                }
            ))

            # Also get catalog items by productId for items without skuId
            product_ids = [int(item['productId']) for item in order.get('line_items', [])
                         if 'productId' in item and item['productId'] is not None and 'skuId' not in item]
            if product_ids:
                catalog_items_by_product = list(catalog_collection.find(
                    {'productId': {'$in': product_ids}},
                    {
                        'image': 1,
                        'name': 1,
                        'productId': 1,
                        'expansionName': 1,
                        'url': 1,
                        'rarity': 1,
                        'number': 1  # Add card number field
                    }
                ))
                catalog_items.extend(catalog_items_by_product)

            # Create lookup dictionaries for both skuId and productId
            catalog_dict = {}
            product_id_dict = {}
            for item in catalog_items:
                if 'skus' in item and item['skus']:
                    catalog_dict[item['skus'][0]['skuId']] = item
                if 'productId' in item:
                    product_id_dict[item['productId']] = item

            bulk_updates = []
            for item in order.get('line_items', []):
                # Try to get catalog item by skuId first, then by productId
                catalog_item = None
                if 'skuId' in item:
                    catalog_item = catalog_dict.get(item['skuId'])
                elif 'productId' in item:
                    try:
                        product_id = int(item['productId'])
                        catalog_item = product_id_dict.get(product_id)
                    except (ValueError, TypeError):
                        logger.warning(f"Invalid productId format: {item['productId']}")
                if catalog_item:
                    item['image'] = catalog_item.get('image')
                    item['availableSkus'] = catalog_item.get('skus', [])
                    item['catalogName'] = catalog_item.get('name')
                    item['productId'] = catalog_item.get('productId')
                    item['expansionName'] = catalog_item.get('expansionName')
                    item['url'] = str(catalog_item.get('url', '')) if catalog_item else ''
                    item['rarity'] = catalog_item.get('rarity', 'Unknown')
                    item['number'] = catalog_item.get('number', 'N/A')  # Add card number

                    sku_id = str(item['skuId'])
                    sku_cost = cost_of_goods.get(sku_id, {})
                    item['cost_of_goods'] = sku_cost.get('average_cost', 0)
                    item['purchase_count'] = sku_cost.get('purchase_count', 0)
                    logger.info(f"Added cost of goods for SKU {sku_id}: {item['cost_of_goods']}, purchase count: {item['purchase_count']}")
                else:
                    logger.warning(f"No catalog record found for SKU {item['skuId']}")
                    item['image'] = None
                    item['availableSkus'] = []
                    item['catalogName'] = 'Not found in catalog'
                    item['productId'] = None
                    item['cost_of_goods'] = 0
                    item['purchase_count'] = 0

                bulk_updates.append(
                    UpdateOne(
                        {'_id': order_id, 'line_items.skuId': item['skuId']},
                        {'$set': {f'line_items.$.{k}': v for k, v in item.items()}}
                    )
                )

            if bulk_updates:
                result = order_collection.bulk_write(bulk_updates)
                logger.info(f"Bulk update result: {result.modified_count} document(s) modified")

            # Convert ObjectId to string for JSON serialization
            order['_id'] = str(order['_id'])

            # Convert any other ObjectId fields in the order
            if 'original_order' in order and '_id' in order['original_order']:
                order['original_order']['_id'] = str(order['original_order']['_id'])

            # Convert ObjectId in line items
            for item in order.get('line_items', []):
                if '_id' in item:
                    item['_id'] = str(item['_id'])

            logger.info("Rendering process_order.html template")
            response = make_response(render_template('process_order.html', order=order, tcgplayer_url=order.get('url'), page=page, total_pages=total_pages))
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
            return response
        else:
            logger.warning(f"Order not found: {order_id}")
            flash('Order not found', 'error')
            return redirect(url_for('buylist.buylist_orders'))
    except Exception as e:
        logger.error(f"Error processing order: {str(e)}", exc_info=True)
        flash('An error occurred while processing the order', 'error')
        return redirect(url_for('buylist.buylist_orders'))

@buylist_bp.route('/api/record-staff-access', methods=['POST'])
@login_required
def record_staff_access():
    try:
        data = request.json
        order_id = data.get('orderId')
        staff_name = data.get('staffName')
        access_time = datetime.utcnow()

        result = order_collection.update_one(
            {'_id': ObjectId(order_id)},
            {'$push': {'staff_access': {'name': staff_name, 'time': access_time}}}
        )

        if result.modified_count > 0:
            return jsonify({'success': True, 'message': 'Staff access recorded successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to record staff access'}), 500
    except Exception as e:
        logger.error(f"Error recording staff access: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': 'An error occurred while recording staff access'}), 500

@buylist_bp.route('/api/get-staff-access/<order_id>')
@login_required
def get_staff_access(order_id):
    try:
        order = order_collection.find_one({'_id': ObjectId(order_id)})
        if not order:
            return jsonify({'success': False, 'error': 'Order not found'}), 404

        staff_access = order.get('staff_access', [])
        for record in staff_access:
            if 'time' in record and isinstance(record['time'], datetime):
                record['time'] = record['time'].isoformat()

        return jsonify({'success': True, 'records': staff_access})
    except Exception as e:
        logger.error(f"Error fetching staff access records: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': 'An error occurred while fetching staff access records'}), 500

@buylist_bp.route('/api/get-stock-info', methods=['POST'])
@login_required
def get_stock_info():
    from routes.buylist.core import db

    data = request.get_json()
    product_id = data.get('productId')
    username = current_user.username

    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400

    try:
        product = db.shProducts.find_one({'tor': {'$regex': f'.*{product_id}.*'}})

        if not product:
            return jsonify({'stock': 0, 'message': 'Product not found in inventory'})

        total_stock = sum(variant.get('quantity', 0) for variant in product.get('variants', []))

        return jsonify({'stock': total_stock, 'message': 'Stock information retrieved successfully'})
    except Exception as e:
        logger.error(f"Error fetching stock information: {str(e)}", exc_info=True)
        return jsonify({'error': 'An error occurred while fetching stock information'}), 500

@buylist_bp.route('/api/search')
@login_required
def search_catalog():
    term = request.args.get('term', '')
    exp_number = request.args.get('expNumber', '')

    name_pattern = re.compile(f'.*{re.escape(term)}.*', re.IGNORECASE)

    query = {'name': name_pattern}

    if exp_number:
        query['number'] = exp_number

    results = list(catalog_collection.find(query).limit(50))

    processed_results = []
    for result in results:
        result['_id'] = str(result['_id'])

        print_types = set()
        conditions = set()
        languages = set()

        for sku in result.get('skus', []):
            print_types.add(sku.get('printingName', ''))
            conditions.add(sku.get('condAbbr', ''))
            languages.add(sku.get('langAbbr', ''))

        result['printingName_options'] = list(print_types)
        result['condAbbr_options'] = list(conditions)
        result['langAbbr_options'] = list(languages)

        processed_results.append(result)

    return jsonify({'results': processed_results})

@buylist_bp.route('/top-purchased-products', methods=['GET'])
@login_required
def top_purchased_products():
    return render_template('buylist_top_purchased_products.html')

@buylist_bp.route('/api/get-top-purchased-products', methods=['GET'])
@login_required
def get_top_purchased_products():
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    try:
        start_date = datetime.strptime(start_date, '%Y-%m-%d') if start_date else None
        end_date = datetime.strptime(end_date, '%Y-%m-%d') if end_date else None
    except ValueError:
        return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD.'}), 400

    query = {'username': current_user.username}
    if start_date:
        query['date'] = {'$gte': start_date}
    if end_date:
        query['date'] = query.get('date', {})
        query['date']['$lte'] = end_date + timedelta(days=1)  # Include the end date

    pipeline = [
        {'$match': query},
        {'$unwind': '$line_items'},
        {'$group': {
            '_id': '$line_items.productId',
            'name': {'$first': '$line_items.name'},
            'total_quantity': {'$sum': '$line_items.quantity'},
            'total_revenue': {'$sum': {'$multiply': ['$line_items.quantity', '$line_items.offeredPrice']}}
        }},
        {'$sort': {'total_quantity': -1}},
        {'$limit': 20}
    ]

    top_products = list(order_collection.aggregate(pipeline))

    return jsonify(top_products)

@buylist_bp.route('/view-order/<token>', methods=['GET'])
def view_order(token):
    try:
        s = URLSafeTimedSerializer(current_app.config['SECRET_KEY'])
        order_id = s.loads(token, max_age=604800)  # Token expires after 7 days
        order = order_collection.find_one({'_id': ObjectId(order_id)})
        if not order:
            flash('Order not found or link has expired', 'error')
            return redirect(url_for('tcgland.index'))

        return render_template('view_order.html', order=order)
    except:
        flash('Invalid or expired link', 'error')
        return redirect(url_for('tcgland.index'))

@buylist_bp.route('/api/check-customer', methods=['GET'])
@login_required
def check_customer():
    try:
        email = request.args.get('email')
        if not email:
            return jsonify({'exists': False, 'error': 'Email parameter is required'}), 400

        # Check if customer exists in shCustomers collection
        from routes.buylist.core import db
        customer = db.shCustomers.find_one({'email': email})

        return jsonify({'exists': customer is not None})
    except Exception as e:
        logger.error(f"Error checking customer: {str(e)}", exc_info=True)
        return jsonify({'exists': False, 'error': str(e)}), 500

@buylist_bp.route('/api/check-payment-status', methods=['GET'])
@login_required
def check_payment_status():
    try:
        order_id = request.args.get('orderId')
        if not order_id:
            return jsonify({'paid': False, 'error': 'OrderId parameter is required'}), 400

        # Check if payment is recorded in the buylistPayments collection
        from routes.buylist.core import db
        payment_record = db.buylistPayments.find_one({'orderId': order_id})

        result = {
            'paid': payment_record is not None,
            'paidDate': payment_record.get('paidDate') if payment_record else None,
            'notes': payment_record.get('notes', '') if payment_record else ''
        }

        return jsonify(result)
    except Exception as e:
        logger.error(f"Error checking payment status: {str(e)}", exc_info=True)
        return jsonify({'paid': False, 'error': str(e)}), 500

@buylist_bp.route('/api/record-payment', methods=['POST'])
@login_required
def record_payment():
    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400

        # Extract data from request
        order_id = data.get('orderId')
        cash_total = float(data.get('cashTotal', 0))
        credit_total = float(data.get('creditTotal', 0))
        customer_email = data.get('customerEmail')
        paid_date = data.get('paidDate', datetime.utcnow().isoformat())
        notes = data.get('notes', '')

        if not order_id:
            return jsonify({'success': False, 'message': 'Order ID is required'}), 400

        if not customer_email:
            return jsonify({'success': False, 'message': 'Customer email is required'}), 400

        from routes.buylist.core import db

        # Initialize response data
        response_data = {
            'success': True,
            'cashPaymentSuccess': False,
            'creditPaymentSuccess': False,
            'message': ''
        }

        # Process credit portion first if it exists
        if credit_total > 0:
            logger.info(f"Processing credit portion of {credit_total} for order {order_id}")

            # Find the customer in shCustomers collection to get Shopify customer ID
            shopify_customer = db.shCustomers.find_one({'email': customer_email, 'username': current_user.username})

            if not shopify_customer:
                logger.warning(f"Customer with email {customer_email} not found in Shopify customers")
                response_data['creditPaymentSuccess'] = False
                response_data['creditMessage'] = f"Customer with email {customer_email} not found in Shopify"
            else:
                # Get the Shopify customer ID
                shopify_customer_id = shopify_customer.get('id')

                if not shopify_customer_id:
                    logger.warning(f"Shopify customer ID not found for email {customer_email}")
                    response_data['creditPaymentSuccess'] = False
                    response_data['creditMessage'] = "Shopify customer ID not found"
                else:
                    try:
                        # Import User model to get Shopify credentials
                        from models.user_model import User
                        user = User.objects(username=current_user.username).first()

                        if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                            logger.error("Shopify credentials not found")
                            response_data['creditPaymentSuccess'] = False
                            response_data['creditMessage'] = "Shopify credentials not found"
                        else:
                            # Get user's currency
                            currency = user.currency if hasattr(user, 'currency') and user.currency else 'GBP'

                            # Prepare GraphQL mutation to add store credit
                            mutation = """
                            mutation {
                              storeCreditAccountCredit(
                                id: "gid://shopify/Customer/%s",
                                creditInput: {
                                  creditAmount: {
                                    amount: "%s"
                                    currencyCode: %s
                                  }
                                }
                              ) {
                                storeCreditAccountTransaction {
                                  id
                                  amount {
                                    amount
                                    currencyCode
                                  }
                                }
                                userErrors {
                                  field
                                  message
                                }
                              }
                            }
                            """ % (shopify_customer_id, str(credit_total), currency)

                            # Execute GraphQL request
                            # Helper function for GraphQL requests
                            async def execute_graphql_request(user, query, variables=None):
                                """Execute a GraphQL request to Shopify"""
                                import aiohttp

                                if not user or not user.shopifyAccessToken or not user.shopifyStoreName:
                                    raise Exception("Shopify credentials not found")

                                shopify_store_name = user.shopifyStoreName
                                api_key = user.shopifyAccessToken

                                graphql_endpoint = f"https://{shopify_store_name}.myshopify.com/admin/api/2025-04/graphql.json"

                                headers = {
                                    'X-Shopify-Access-Token': api_key,
                                    'Content-Type': 'application/json'
                                }

                                async with aiohttp.ClientSession() as session:
                                    async with session.post(
                                        graphql_endpoint,
                                        headers=headers,
                                        json={"query": query, "variables": variables or {}}
                                    ) as response:
                                        if response.status == 200:
                                            return await response.json()
                                        else:
                                            error_text = await response.text()
                                            raise Exception(f"Error from Shopify API: {response.status} - {error_text}")

                            # Synchronous wrapper for GraphQL requests
                            def execute_graphql(user, query, variables=None):
                                """Synchronous wrapper for GraphQL requests"""
                                import asyncio

                                try:
                                    loop = asyncio.new_event_loop()
                                    asyncio.set_event_loop(loop)
                                    result = loop.run_until_complete(execute_graphql_request(user, query, variables))
                                    loop.close()
                                    return result
                                except Exception as e:
                                    raise Exception(f"GraphQL request failed: {str(e)}")

                            # Execute the GraphQL request
                            result = execute_graphql(user, mutation)

                            # Check for user errors
                            user_errors = result.get('data', {}).get('storeCreditAccountCredit', {}).get('userErrors', [])
                            if user_errors:
                                error_messages = [error.get('message') for error in user_errors]
                                logger.error(f"Shopify API errors: {', '.join(error_messages)}")
                                response_data['creditPaymentSuccess'] = False
                                response_data['creditMessage'] = f"Shopify API errors: {', '.join(error_messages)}"
                            else:
                                # Check if credit was added successfully
                                transaction = result.get('data', {}).get('storeCreditAccountCredit', {}).get('storeCreditAccountTransaction', {})
                                if transaction:
                                    amount_obj = transaction.get('amount', {})
                                    amount = amount_obj.get('amount')
                                    currency = amount_obj.get('currencyCode')

                                    # Record the transaction in MongoDB for history
                                    transaction_doc = {
                                        'transaction_id': transaction.get('id'),
                                        'customer_id': shopify_customer_id,
                                        'amount': float(amount),
                                        'currency': currency,
                                        'type': 'credit',
                                        'note': f"Buylist order credit: {order_id}",
                                        'created_at': datetime.utcnow(),
                                        'username': current_user.username
                                    }
                                    db.shStoreCreditTransactions.insert_one(transaction_doc)

                                    logger.info(f"Successfully added {amount} {currency} credit to customer {shopify_customer_id}")
                                    response_data['creditPaymentSuccess'] = True
                                    response_data['creditMessage'] = f"Successfully added {amount} {currency} credit"
                                    response_data['creditTransaction'] = transaction_doc
                                else:
                                    logger.error("Unexpected response format from Shopify")
                                    response_data['creditPaymentSuccess'] = False
                                    response_data['creditMessage'] = "Unexpected response format from Shopify"
                    except Exception as e:
                        logger.error(f"Error adding store credit: {str(e)}")
                        response_data['creditPaymentSuccess'] = False
                        response_data['creditMessage'] = f"Error adding store credit: {str(e)}"

        # Process cash portion
        payment_record = {
            'orderId': order_id,
            'cashTotal': cash_total,
            'creditTotal': credit_total,
            'customerEmail': customer_email,
            'paidDate': paid_date,
            'notes': notes,
            'creditProcessed': response_data.get('creditPaymentSuccess', False),
            'creditTransactionId': response_data.get('creditTransaction', {}).get('transaction_id', None) if response_data.get('creditPaymentSuccess', False) else None,
            'updatedAt': datetime.utcnow(),
            'username': current_user.username
        }

        # Check if payment already exists
        existing_payment = db.buylistPayments.find_one({'orderId': order_id})
        if existing_payment:
            # Update existing payment record
            result = db.buylistPayments.update_one(
                {'orderId': order_id},
                {'$set': payment_record}
            )

            cash_success = result.modified_count > 0
            cash_message = 'Payment record updated successfully' if cash_success else 'No changes made to payment record'
        else:
            # Create new payment record
            payment_record['createdAt'] = datetime.utcnow()
            result = db.buylistPayments.insert_one(payment_record)

            # Also update the order status to mark as paid
            order_collection.update_one(
                {'_id': ObjectId(order_id)},
                {'$set': {'paymentRecorded': True, 'paymentDate': datetime.utcnow()}}
            )

            cash_success = result.inserted_id is not None
            cash_message = 'Payment record created successfully' if cash_success else 'Failed to create payment record'

        response_data['cashPaymentSuccess'] = cash_success
        response_data['cashMessage'] = cash_message

        # Determine overall success and message
        if credit_total > 0 and cash_total > 0:
            if response_data['creditPaymentSuccess'] and response_data['cashPaymentSuccess']:
                response_data['success'] = True
                response_data['message'] = 'Both cash and credit payments processed successfully'
            elif response_data['creditPaymentSuccess']:
                response_data['success'] = True
                response_data['message'] = 'Credit payment processed successfully, but cash payment failed'
            elif response_data['cashPaymentSuccess']:
                response_data['success'] = True
                response_data['message'] = 'Cash payment processed successfully, but credit payment failed'
            else:
                response_data['success'] = False
                response_data['message'] = 'Both cash and credit payments failed'
        elif credit_total > 0:
            response_data['success'] = response_data['creditPaymentSuccess']
            response_data['message'] = response_data['creditMessage']
        else:
            response_data['success'] = response_data['cashPaymentSuccess']
            response_data['message'] = response_data['cashMessage']

        logger.info(f"Payment record {'updated' if existing_payment else 'created'} for order {order_id}")
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error recording payment: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'An error occurred: {str(e)}'}), 500

@buylist_bp.route('/view_order/<order_id>', methods=['GET'])
@login_required
def view_order_by_id(order_id):
    try:
        order = order_collection.find_one({'_id': ObjectId(order_id)})
        if not order:
            flash('Order not found', 'error')
            return redirect(url_for('buylist.buylist_orders'))

        # First, collect all unique productIds from the order
        all_product_ids = set()
        for item in order.get('line_items', []):
            if 'productId' in item:
                all_product_ids.add(item['productId'])

        # Convert string productIds to int for catalog lookup
        product_ids_to_lookup = []
        product_id_mapping = {}  # Map string productId to int productId

        # Check which productIds we need to look up (not in cache)
        for product_id_str in all_product_ids:
            if product_id_str in expansion_name_cache:
                logger.info(f"Using cached expansion name for product ID {product_id_str}: {expansion_name_cache[product_id_str]}")
                continue

            try:
                product_id_int = int(product_id_str)
                product_ids_to_lookup.append(product_id_int)
                product_id_mapping[product_id_str] = product_id_int
            except (ValueError, TypeError):
                logger.warning(f"Invalid productId format: {product_id_str}")

        # Look up expansion names in catalog collection for product IDs not in cache
        if product_ids_to_lookup:
            logger.info(f"Looking up expansion names for {len(product_ids_to_lookup)} product IDs")
            catalog_items = catalog_collection.find(
                {'productId': {'$in': product_ids_to_lookup}},
                {'productId': 1, 'expansionName': 1}
            )

            for item in catalog_items:
                if 'productId' in item and 'expansionName' in item:
                    # Store with string key for easier lookup and cache it
                    product_id_str = str(item['productId'])
                    expansion_name_cache[product_id_str] = item['expansionName']
                    logger.info(f"Found and cached expansion name for product ID {product_id_str}: {item['expansionName']}")

        # Create a mapping of name to expansion for items that don't have productId
        name_to_expansion = {}
        for item in order.get('line_items', []):
            if 'expansionName' in item and item['expansionName'] != 'N/A' and 'name' in item:
                name_to_expansion[item['name']] = item['expansionName']

        # Update all line items with expansion names
        for item in order.get('line_items', []):
            # First try to get expansion name from the item itself
            if 'expansionName' not in item or item['expansionName'] == 'N/A':
                # Then try to get it from the cache by productId
                if 'productId' in item and item['productId'] in expansion_name_cache:
                    item['expansionName'] = expansion_name_cache[item['productId']]
                    logger.info(f"Set expansion name for {item.get('name')} from cache: {item['expansionName']}")
                # Then try to get it from other items with the same name
                elif 'name' in item and item['name'] in name_to_expansion:
                    item['expansionName'] = name_to_expansion[item['name']]
                    logger.info(f"Set expansion name for {item['name']} from other items: {item['expansionName']}")
                # If all else fails, set to N/A
                else:
                    item['expansionName'] = 'N/A'

            # Ensure printType exists
            if 'printType' not in item:
                item['printType'] = 'Regular'

        return render_template('view_order.html', order=order)
    except Exception as e:
        logger.error(f"Error viewing order {order_id}: {str(e)}", exc_info=True)
        flash('An error occurred while viewing the order', 'error')
        return redirect(url_for('buylist.buylist_orders'))

# Import missing dependencies
from pymongo import UpdateOne
from datetime import timedelta
