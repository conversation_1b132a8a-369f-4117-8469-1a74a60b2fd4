import requests
import logging
import concurrent.futures
from datetime import datetime, timedelta
from routes.buylist.core import logger, tcgplayer_key_collection, catalog_collection, inventory_collection, price_cache_collection

def get_tcgplayer_api_key():
    """
    Get the latest TCGPlayer API key from the database.
    
    Returns:
        str: The latest TCGPlayer API key
        
    Raises:
        Exception: If no valid API key is found or if the key is expired
    """
    try:
        # Try to get the most recent key document by expiresAt date
        key_doc = tcgplayer_key_collection.find_one(
            {'expiresAt': {'$gt': datetime.utcnow()}},  # Only get non-expired keys
            sort=[('expiresAt', -1)]  # Get the one that expires furthest in the future
        )
        
        # If no non-expired key is found, try to get the most recent key by _id
        if not key_doc:
            key_doc = tcgplayer_key_collection.find_one({}, sort=[('_id', -1)])
            logger.warning("No non-expired TCGPlayer API key found, using most recent key")
        
        if not key_doc:
            logger.error("No TCGPlayer API key found in the database")
            raise Exception("No TCGPlayer API key found in the database")
        
        # Log the key document for debugging
        logger.info(f"Found TCGPlayer key document: ID={key_doc.get('_id')}, Fields: {list(key_doc.keys())}")
        
        # Check if the key is expired
        if 'expiresAt' in key_doc and key_doc['expiresAt'] < datetime.utcnow():
            logger.warning(f"TCGPlayer API key expired at {key_doc['expiresAt']}")
        
        # Check for the key field
        if 'latestKey' in key_doc:
            logger.info("Using 'latestKey' field for API key")
            return key_doc['latestKey']
        
        # If 'latestKey' is not found, try other possible field names
        for key_field in ['latest_key', 'key', 'apiKey', 'api_key', 'token', 'access_token', 'bearer_token']:
            if key_field in key_doc:
                logger.info(f"Using '{key_field}' field for API key")
                return key_doc[key_field]
        
        # If no key field is found, log the error and raise an exception
        logger.error(f"TCGPlayer API key field not found in the document. Available fields: {list(key_doc.keys())}")
        raise Exception("TCGPlayer API key field not found in the document")
    
    except Exception as e:
        logger.error(f"Error retrieving TCGPlayer API key: {str(e)}")
        raise Exception(f"Error retrieving TCGPlayer API key: {str(e)}")

def fetch_batch_prices(batch, api_key, batch_num):
    """
    Helper function to fetch prices for a batch of SKUs.
    
    Args:
        batch (list): List of SKU IDs to fetch
        api_key (str): TCGPlayer API key
        batch_num (int): Batch number for logging
        
    Returns:
        dict: Dictionary mapping SKU IDs to price data
    """
    batch_results = {}
    
    try:
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json'
        }
        
        # Join SKU IDs with commas for the API request
        sku_ids_param = ','.join(batch)
        
        # Make the API request
        logger.info(f"Making API request to TCGPlayer for SKUs batch {batch_num}: {sku_ids_param}")
        response = requests.get(f'https://api.tcgplayer.com/pricing/sku/{sku_ids_param}', headers=headers)
        
        # Log the full response for debugging
        logger.info(f"TCGPlayer API raw response status: {response.status_code}")
        logger.info(f"TCGPlayer API raw response headers: {response.headers}")
        
        # Check if the response is successful
        if response.status_code != 200:
            logger.error(f"TCGPlayer API error: Status {response.status_code}, Response: {response.text}")
            # Return empty results for this batch
            return batch_results
            
        response.raise_for_status()  # Raise exception for HTTP errors
        
        # Process the response
        response_json = response.json()
        
        # Log the full response for debugging
        logger.info(f"TCGPlayer API response for batch {batch_num}: {response_json}")
        
        # Check if the response has the expected structure
        if 'results' not in response_json:
            logger.error(f"Unexpected API response format: {response_json}")
            return batch_results
            
        # Process each result
        results = response_json.get('results', [])
        logger.info(f"Processing {len(results)} results from batch {batch_num}")
        
        # Create a mapping of requested SKUs to track which ones were returned
        requested_skus = {sku_id: False for sku_id in batch}
        
        for price_data in results:
            sku_id = str(price_data.get('skuId'))
            if not sku_id:
                logger.warning(f"Skipping result with no SKU ID: {price_data}")
                continue
                
            # Mark this SKU as returned
            if sku_id in requested_skus:
                requested_skus[sku_id] = True
                
            # Check if we have actual price data
            has_price_data = (
                price_data.get('lowPrice') is not None or 
                price_data.get('marketPrice') is not None or
                price_data.get('midPrice') is not None or
                price_data.get('highPrice') is not None
            )
            
            if not has_price_data:
                logger.warning(f"No price data for SKU ID {sku_id}: {price_data}")
                # Try to get price from direct market API instead
                direct_price = fetch_direct_market_price(sku_id)
                if direct_price > 0:
                    logger.info(f"Using direct market price for SKU ID {sku_id}: ${direct_price}")
                    price_info = {
                        "price": direct_price,
                        "marketPrice": direct_price,
                        "lowPrice": direct_price,
                        "midPrice": direct_price,
                        "highPrice": direct_price,
                        "source": "direct_market"
                    }
                    batch_results[sku_id] = price_info
                    continue
            
            # Extract price data, ensuring we have numeric values
            low_price = float(price_data.get('lowPrice', 0) or 0)  # Convert None to 0
            market_price = float(price_data.get('marketPrice', low_price) or 0)  # Convert None to 0
            mid_price = float(price_data.get('midPrice', 0) or 0)  # Convert None to 0
            high_price = float(price_data.get('highPrice', 0) or 0)  # Convert None to 0
            
            # If all prices are 0, try to get price from direct market API
            if low_price == 0 and market_price == 0:
                direct_price = fetch_direct_market_price(sku_id)
                if direct_price > 0:
                    logger.info(f"Using direct market price for SKU ID {sku_id}: ${direct_price}")
                    low_price = direct_price
                    market_price = direct_price
            
            price_info = {
                "price": low_price,  # Use lowPrice as the main price
                "marketPrice": market_price,
                "lowPrice": low_price,
                "midPrice": mid_price,
                "highPrice": high_price,
                "source": "tcgplayer_api"
            }
            
            batch_results[sku_id] = price_info
            logger.info(f"Got price data for SKU ID {sku_id}: lowPrice=${low_price}, marketPrice=${market_price}")
        
        # Check for SKUs that were requested but not returned
        missing_skus = [sku_id for sku_id, found in requested_skus.items() if not found]
        if missing_skus:
            logger.warning(f"SKUs not found in API response: {missing_skus}")
            
            # Try to get prices for missing SKUs from direct market API
            for sku_id in missing_skus:
                direct_price = fetch_direct_market_price(sku_id)
                if direct_price > 0:
                    logger.info(f"Using direct market price for missing SKU ID {sku_id}: ${direct_price}")
                    batch_results[sku_id] = {
                        "price": direct_price,
                        "marketPrice": direct_price,
                        "lowPrice": direct_price,
                        "midPrice": direct_price,
                        "highPrice": direct_price,
                        "source": "direct_market"
                    }
    
    except Exception as e:
        logger.error(f"Error fetching prices for batch {batch_num}: {str(e)}", exc_info=True)
    
    return batch_results

def fetch_direct_market_price(sku_id):
    """
    Fetch price data for a specific SKU from a direct market API.
    This is a fallback when TCGPlayer API doesn't return price data.
    
    Args:
        sku_id (str): The SKU ID to fetch price data for
        
    Returns:
        float: The price from the direct market API, or 0 if not found
    """
    try:
        # Try to get price from catalog collection first
        catalog_item = catalog_collection.find_one({'skus.skuId': int(sku_id)})
        if catalog_item and catalog_item.get('skus'):
            for sku in catalog_item['skus']:
                if str(sku.get('skuId')) == str(sku_id):
                    low_price = sku.get('lowPrice', 0) or 0
                    market_price = sku.get('marketPrice', 0) or 0
                    price = low_price if low_price > 0 else market_price
                    if price > 0:
                        logger.info(f"Found price in catalog for SKU ID {sku_id}: ${price}")
                        return float(price)
        
        # If no price in catalog, try to get from inventory
        inventory_item = inventory_collection.find_one({'skuId': str(sku_id)})
        if inventory_item:
            price = inventory_item.get('lowPrice', 0) or 0
            if price > 0:
                logger.info(f"Found price in inventory for SKU ID {sku_id}: ${price}")
                return float(price)
        
        # If still no price, try to get from price cache
        cached_price = price_cache_collection.find_one({'sku_id': str(sku_id)})
        if cached_price and cached_price.get('price_data'):
            price_data = cached_price['price_data']
            low_price = price_data.get('lowPrice', 0) or 0
            market_price = price_data.get('marketPrice', 0) or 0
            price = low_price if low_price > 0 else market_price
            if price > 0:
                logger.info(f"Found price in cache for SKU ID {sku_id}: ${price}")
                return float(price)
        
        # If all else fails, try to get from a third-party API
        # This is just a placeholder - you would need to implement this with a real API
        # For now, we'll just return 0
        return 0
    
    except Exception as e:
        logger.error(f"Error fetching direct market price for SKU ID {sku_id}: {str(e)}")
        return 0

def fetch_tcgplayer_prices_direct(sku_ids):
    """
    Fetch price data for multiple SKUs directly from TCGPlayer API in parallel batched requests.
    TCGPlayer API only allows a maximum of 10 SKUs per API call.
    
    Args:
        sku_ids (list): List of SKU IDs to fetch price data for
        
    Returns:
        dict: Dictionary mapping SKU IDs to price data
    """
    if not sku_ids:
        return {}
    
    # Convert all SKU IDs to strings for consistency
    sku_ids = [str(sku_id) for sku_id in sku_ids]
    
    # Check cache first for all SKUs
    cached_prices = {}
    for doc in price_cache_collection.find({
        'sku_id': {'$in': sku_ids},
        'timestamp': {'$gte': datetime.utcnow() - timedelta(hours=24)}
    }):
        cached_prices[str(doc['sku_id'])] = doc['price_data']
    
    # Log cached prices
    for sku_id, data in cached_prices.items():
        logger.info(f"Using cached price data for SKU ID {sku_id}: lowPrice=${data.get('lowPrice', 0)}, marketPrice=${data.get('marketPrice', 0)}")
    
    # Determine which SKUs need to be fetched
    sku_ids_to_fetch = [sku_id for sku_id in sku_ids if sku_id not in cached_prices]
    
    if not sku_ids_to_fetch:
        logger.info("All SKU prices found in cache")
        return cached_prices
    
    logger.info(f"Fetching prices for {len(sku_ids_to_fetch)} SKUs from TCGPlayer API in parallel batched requests")
    
    try:
        # Get API key
        api_key = get_tcgplayer_api_key()
        
        # Process SKUs in batches of 10 (TCGPlayer API limit)
        batch_size = 10
        batches = []
        for i in range(0, len(sku_ids_to_fetch), batch_size):
            batches.append(sku_ids_to_fetch[i:i + batch_size])
        
        # Use ThreadPoolExecutor to fetch prices for all batches in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(10, len(batches))) as executor:
            # Submit all batch requests
            future_to_batch = {
                executor.submit(fetch_batch_prices, batch, api_key, i+1): i 
                for i, batch in enumerate(batches)
            }
            
            # Process results as they complete
            for future in concurrent.futures.as_completed(future_to_batch):
                batch_index = future_to_batch[future]
                try:
                    batch_results = future.result()
                    
                    # Cache the results and add to cached_prices
                    for sku_id, price_info in batch_results.items():
                        # Cache the price data
                        price_cache_collection.update_one(
                            {'sku_id': sku_id},
                            {'$set': {
                                'price_data': price_info,
                                'timestamp': datetime.utcnow()
                            }},
                            upsert=True
                        )
                        
                        cached_prices[sku_id] = price_info
                        logger.info(f"Cached price data for SKU ID {sku_id} from batch {batch_index+1}")
                
                except Exception as e:
                    logger.error(f"Error processing batch {batch_index+1}: {str(e)}")
    
    except Exception as e:
        logger.error(f"Error fetching prices from TCGPlayer API: {str(e)}")
    
    # For any SKUs that weren't in the cache and weren't returned by the API, fall back to catalog prices
    missing_sku_ids = [sku_id for sku_id in sku_ids_to_fetch if sku_id not in cached_prices]
    if missing_sku_ids:
        logger.warning(f"Falling back to catalog prices for {len(missing_sku_ids)} SKUs")
        
        # Get catalog items for the missing SKUs
        try:
            catalog_items = list(catalog_collection.find(
                {'skus.skuId': {'$in': [int(sku_id) for sku_id in missing_sku_ids]}},
                {'skus.$': 1}
            ))
            
            for catalog_item in catalog_items:
                if not catalog_item.get('skus'):
                    continue
                    
                sku = catalog_item['skus'][0]
                sku_id = str(sku.get('skuId'))
                if not sku_id or sku_id not in missing_sku_ids:
                    continue
                    
                low_price = sku.get('lowPrice', 0) or 0  # Convert None to 0
                market_price = sku.get('marketPrice', low_price) or 0  # Convert None to 0
                
                price_info = {
                    "price": low_price,
                    "marketPrice": market_price,
                    "lowPrice": low_price,
                    "midPrice": sku.get('midPrice', 0) or 0,  # Convert None to 0
                    "highPrice": sku.get('highPrice', 0) or 0,  # Convert None to 0
                    "source": "catalog_fallback"
                }
                
                cached_prices[sku_id] = price_info
                logger.info(f"Using catalog fallback price for SKU ID {sku_id}: lowPrice=${low_price}, marketPrice=${market_price}")
        except Exception as fallback_error:
            logger.error(f"Error falling back to catalog prices: {str(fallback_error)}")
    
    # For any remaining SKUs without prices, use zeros
    for sku_id in sku_ids:
        if sku_id not in cached_prices:
            cached_prices[sku_id] = {
                "price": 0,
                "marketPrice": 0,
                "lowPrice": 0,
                "midPrice": 0,
                "highPrice": 0,
                "error": "No price data available"
            }
            logger.warning(f"No price data available for SKU ID {sku_id}, using zeros")
    
    return cached_prices

def fetch_tcgplayer_price(sku_id):
    """
    Fetch price data for a specific SKU from TCGPlayer API.
    
    Args:
        sku_id (int): The SKU ID to fetch price data for
        
    Returns:
        dict: Price data including price, marketPrice, and lowPrice
    """
    # Use the batch function for a single SKU
    prices = fetch_tcgplayer_prices_direct([sku_id])
    return prices.get(str(sku_id), {
        "price": 0,
        "marketPrice": 0,
        "lowPrice": 0,
        "midPrice": 0,
        "highPrice": 0,
        "error": "Failed to fetch price"
    })

def fetch_tcgplayer_prices_batch(sku_ids):
    """
    Fetch price data for multiple SKUs in parallel from TCGPlayer API.
    
    Args:
        sku_ids (list): List of SKU IDs to fetch price data for
        
    Returns:
        dict: Dictionary mapping SKU IDs to price data
    """
    price_data = {}
    
    # Check cache first for all SKUs
    cached_prices = {doc['sku_id']: doc['price_data'] for doc in price_cache_collection.find({
        'sku_id': {'$in': sku_ids},
        'timestamp': {'$gte': datetime.utcnow() - timedelta(hours=24)}
    })}
    
    # Log cached prices
    for sku_id, data in cached_prices.items():
        logger.info(f"Using cached price data for SKU ID {sku_id}: lowPrice=${data.get('lowPrice', 0)}, marketPrice=${data.get('marketPrice', 0)}")
        price_data[str(sku_id)] = data
    
    # Determine which SKUs need to be fetched
    sku_ids_to_fetch = [sku_id for sku_id in sku_ids if sku_id not in cached_prices]
    
    if not sku_ids_to_fetch:
        logger.info("All SKU prices found in cache")
        return price_data
    
    logger.info(f"Fetching prices for {len(sku_ids_to_fetch)} SKUs from TCGPlayer API")
    
    # Use ThreadPoolExecutor to fetch prices in parallel
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        future_to_sku = {executor.submit(fetch_tcgplayer_price, sku_id): sku_id for sku_id in sku_ids_to_fetch}
        for future in concurrent.futures.as_completed(future_to_sku):
            sku_id = future_to_sku[future]
            try:
                data = future.result()
                price_data[str(sku_id)] = data
            except Exception as e:
                logger.error(f"Error fetching price for SKU ID {sku_id}: {str(e)}")
                # Use default price data for failed fetches
                price_data[str(sku_id)] = {
                    "price": 0,
                    "marketPrice": 0,
                    "lowPrice": 0,
                    "midPrice": 0,
                    "highPrice": 0,
                    "error": f"Error fetching price: {str(e)}"
                }
    
    return price_data

def get_updated_prices(sku_id):
    """
    Get updated prices for a specific SKU.
    
    Args:
        sku_id (int): The SKU ID to fetch updated prices for
        
    Returns:
        dict: Updated price data including buylist prices
    """
    try:
        # First try to get price from TCGPlayer API
        price_data = fetch_tcgplayer_price(sku_id)
        if price_data:
            low_price = price_data.get('lowPrice', 0)
            market_price = price_data.get('marketPrice', low_price)
            
            return {
                'price': low_price,
                'marketPrice': market_price,
                'lowPrice': low_price,
                'buylist_cash': round(low_price * 0.7, 2),
                'buylist_credit': round(low_price * 0.8, 2),
                'source': 'tcgplayer_api'
            }
        
        # Fall back to catalog if API fails
        catalog_item = catalog_collection.find_one({'skus.skuId': int(sku_id)})
        if not catalog_item:
            return None

        sku = next((s for s in catalog_item['skus'] if s['skuId'] == int(sku_id)), None)
        if not sku:
            return None

        market_price = sku.get('marketPrice', 0)
        low_price = sku.get('lowPrice', 0)
        
        price = low_price  # Use lowPrice as the main price

        return {
            'price': price,
            'marketPrice': market_price,
            'lowPrice': low_price,
            'buylist_cash': round(price * 0.7, 2),
            'buylist_credit': round(price * 0.8, 2),
            'source': 'catalog_fallback'
        }
    except Exception as e:
        logger.error(f"Error fetching updated prices: {str(e)}", exc_info=True)
        return None
