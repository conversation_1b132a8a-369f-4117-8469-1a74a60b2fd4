@media screen and (max-width: 768px) {
    body {
        overflow-x: hidden;
    }
    
    /* Hide the rep-container on mobile devices - more specific selector to increase specificity */
    .top-bar .dedicated-rep-section,
    .top-bar .rep-container,
    .dedicated-rep-section,
    .rep-container {
        display: none !important;
    }
    
    /* Adjust the top bar to account for hidden rep-container */
    
    .top-bar {
        justify-content: space-between;
    }

    .sidebar {
        transform: translateX(-100%);
        width: 100%;
        position: fixed;
        height: 100vh;
        z-index: 1000;
        transition: transform 0.3s ease;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0 !important;
        padding: 1rem;
        padding-top: 4rem;
        width: 100% !important;
    }

    .mobile-menu-btn {
        display: block !important;
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 1001;
        background-color: #111827;
        color: #ffffff;
        border: none;
        padding: 0.8rem;
        border-radius: 8px;
        cursor: pointer;
        width: 50px;
        height: 50px;
        font-size: 1.8rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    }

    .nav-link {
        min-height: 50px;
        padding: 15px 20px;
        font-size: 1.1em;
    }

    .nav-link i {
        font-size: 1.3em;
        width: 30px;
    }

    .submenu .nav-link {
        padding-left: 3rem;
        min-height: 45px;
    }

    .sidebar-footer {
        position: relative;
        width: 100%;
        padding: 1.5rem;
        margin-top: auto;
    }

    .sidebar-footer .btn {
        padding: 12px;
        font-size: 1rem;
    }

    .dashboard-header {
        flex-direction: column;
        gap: 1rem;
    }

    .search-bar {
        width: 100%;
        max-width: 100%;
        height: 50px;
        font-size: 1.1em;
        padding: 12px;
    }

    .card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1.2rem;
    }

    .card-title {
        font-size: 1.2em;
    }

    .stat-card .card-text {
        font-size: 1.4em;
    }

    h1 {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
    }

    h2 {
        font-size: 1.5rem;
        margin-bottom: 1.2rem;
    }

    /* Improve modal for mobile */
    .modal-dialog {
        margin: 0.5rem;
    }

    .modal-content {
        border-radius: 12px;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .form-control {
        font-size: 1.1em;
        padding: 12px;
    }

    .btn {
        padding: 12px 20px;
        font-size: 1.1em;
    }



    /* Enhanced Pricing Cards Mobile */
    .pricing-cards-container {
        gap: 1rem !important;
    }

    .pricing-card {
        border-radius: 20px !important;
        padding: 1.5rem !important;
        margin-bottom: 1rem !important;
    }

    .pricing-card .platform-icon {
        width: 70px !important;
        height: 70px !important;
        border-radius: 20px !important;
        margin: 1rem auto 1rem !important;
    }

    .pricing-card .platform-icon i {
        font-size: 32px !important;
    }

    .bundle-card .platform-icon {
        width: 80px !important;
        height: 80px !important;
        border-radius: 24px !important;
    }

    .bundle-card .platform-icon i {
        font-size: 36px !important;
    }

    .pricing-card .platform-title {
        font-size: 1.2rem !important;
        margin-bottom: 0.8rem !important;
    }

    .pricing-card .price-display {
        font-size: 2.2rem !important;
        margin-bottom: 0.3rem !important;
    }

    .bundle-card .price-display {
        font-size: 2.5rem !important;
    }

    .pricing-card .features-list {
        font-size: 0.9rem !important;
        margin-bottom: 1.2rem !important;
    }

    .pricing-card .features-list div {
        margin-bottom: 6px !important;
    }

    .pricing-card .features-list i {
        font-size: 14px !important;
        margin-right: 10px !important;
    }

    .cta-button {
        padding: 12px 24px !important;
        font-size: 1rem !important;
        border-radius: 25px !important;
        width: 100% !important;
        max-width: 250px !important;
    }

    .popular-badge {
        padding: 6px 16px !important;
        font-size: 0.7rem !important;
        border-radius: 20px !important;
        top: -12px !important;
    }

    .platform-badge {
        padding: 4px 12px !important;
        font-size: 0.7rem !important;
        border-radius: 15px !important;
        top: -10px !important;
    }

    .savings-badge {
        font-size: 1.1rem !important;
        padding: 3px 12px !important;
        margin-bottom: 1.2rem !important;
    }

    .pricing-note {
        padding: 10px !important;
        border-radius: 12px !important;
    }

    .guarantee {
        font-size: 0.85rem !important;
    }
}

/* Small phones */
@media screen and (max-width: 480px) {
    /* Ensure rep-container remains hidden on small phones - with higher specificity */
    .top-bar .dedicated-rep-section,
    .top-bar .rep-container,
    .dedicated-rep-section,
    .rep-container {
        display: none !important;
    }
    
    .main-content {
        padding: 0.8rem;
        padding-top: 4rem;
    }

    .card {
        margin-bottom: 0.8rem;
    }

    .card-body {
        padding: 1rem;
    }

    .nav-link {
        padding: 12px 15px;
    }

    .profile-section {
        padding: 1rem;
    }

    .sidebar-footer {
        padding: 1rem;
    }

    .sidebar-footer .btn {
        padding: 10px;
        font-size: 0.9rem;
    }


}

/* Landscape orientation */
@media screen and (max-height: 480px) and (orientation: landscape) {
    /* Ensure rep-container remains hidden in landscape orientation - with higher specificity */
    .top-bar .dedicated-rep-section,
    .top-bar .rep-container,
    .dedicated-rep-section,
    .rep-container {
        display: none !important;
    }
    
    .sidebar {
        position: absolute;
    }

    .main-content {
        margin-top: 0;
    }

    .nav-link {
        min-height: 40px;
        padding: 10px 15px;
    }

    .sidebar-footer {
        position: relative;
        padding: 0.8rem;
    }

    .sidebar-footer .btn {
        padding: 8px;
        font-size: 0.9rem;
    }


}
