{% extends "base.html" %}

{% block title %}Pricing Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-tags me-2" style="color: #009688;"></i>
                Pricing Dashboard
            </h1>
            <p class="text-muted mb-0">Manage and monitor your product pricing</p>
        </div>
        <div>
            <a href="{{ url_for('pricing.bulk_update') }}" class="btn btn-primary me-2">
                <i class="fas fa-edit me-1"></i>
                Bulk Update
            </a>
            <a href="{{ url_for('pricing.pricing_rules') }}" class="btn btn-outline-secondary">
                <i class="fas fa-cogs me-1"></i>
                Pricing Rules
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Products
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_products }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Advanced Pricing
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ products_with_custom_pricing }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Recent Changes (30 days)
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ recent_pricing_changes }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-history fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Average Price
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">£{{ avg_price }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-pound-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Price Distribution Chart -->
    <div class="row mb-4">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Price Distribution</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="priceDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="{{ url_for('pricing.bulk_update') }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-edit me-2 text-primary"></i>
                                    Bulk Price Update
                                </h6>
                            </div>
                            <p class="mb-1">Update multiple product prices at once</p>
                        </a>
                        <a href="{{ url_for('pricing.pricing_rules') }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-cogs me-2 text-success"></i>
                                    Pricing Rules
                                </h6>
                            </div>
                            <p class="mb-1">Set up automated pricing rules</p>
                        </a>
                        <a href="{{ url_for('pricing.pricing_history') }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-history me-2 text-info"></i>
                                    Pricing History
                                </h6>
                            </div>
                            <p class="mb-1">View all pricing changes</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Pricing History -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Pricing Changes</h6>
                    <a href="{{ url_for('pricing.pricing_history') }}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_history %}
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Vendor</th>
                                    <th>Old Price</th>
                                    <th>New Price</th>
                                    <th>Change</th>
                                    <th>Reason</th>
                                    <th>Updated By</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for history in recent_history %}
                                <tr>
                                    <td>{{ history.product_name or 'N/A' }}</td>
                                    <td>{{ history.vendor or 'N/A' }}</td>
                                    <td>£{{ "%.2f"|format(history.old_price) }}</td>
                                    <td>£{{ "%.2f"|format(history.new_price) }}</td>
                                    <td>
                                        {% set change = history.new_price - history.old_price %}
                                        {% if change > 0 %}
                                            <span class="text-success">+£{{ "%.2f"|format(change) }}</span>
                                        {% elif change < 0 %}
                                            <span class="text-danger">£{{ "%.2f"|format(change) }}</span>
                                        {% else %}
                                            <span class="text-muted">£0.00</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ history.change_description or history.change_reason or 'N/A' }}</td>
                                    <td>{{ history.updated_by }}</td>
                                    <td>{{ history.updated_at.strftime('%Y-%m-%d %H:%M') if history.updated_at else 'N/A' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-gray-300 mb-3"></i>
                        <p class="text-muted">No recent pricing changes found.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Price Distribution Chart
const ctx = document.getElementById('priceDistributionChart').getContext('2d');
const priceDistributionChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['Under £5', '£5 - £20', '£20 - £50', 'Over £50'],
        datasets: [{
            data: [
                {{ price_ranges.under_5 }},
                {{ price_ranges.between_5_20 }},
                {{ price_ranges.between_20_50 }},
                {{ price_ranges.over_50 }}
            ],
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e'
            ],
            hoverBackgroundColor: [
                '#2e59d9',
                '#17a673',
                '#2c9faf',
                '#f4b619'
            ],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        maintainAspectRatio: false,
        tooltips: {
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            caretPadding: 10,
        },
        legend: {
            display: true,
            position: 'bottom'
        },
        cutoutPercentage: 80,
    },
});
</script>

{% endblock %}
