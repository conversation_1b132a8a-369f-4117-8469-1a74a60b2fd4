from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from models.database import mongo
from cache_config import cache
import requests

price_bp = Blueprint('manual_inventory_price', __name__)

# Hardcoded conversion rates (USD to other currencies)
CONVERSION_RATES = {
    'USD': 1.0,
    'EUR': 0.92,  # 1 USD = 0.92 EUR
    'GBP': 0.79,  # 1 USD = 0.79 GBP
    'CAD': 1.35,  # 1 USD = 1.35 CAD
    'AUD': 1.52,  # 1 USD = 1.52 AUD
    'JPY': 148.0  # 1 USD = 148 JPY
}

def get_tcgplayer_token():
    try:
        # Get the latest TCGPlayer key from the collection
        key_doc = mongo.db.tcgplayerKey.find_one({}, sort=[('_id', -1)])
        if key_doc and 'latestKey' in key_doc:
            return key_doc['latestKey']
    except Exception as e:
        print(f"Error getting TCGPlayer token: {str(e)}")
    return None

@price_bp.route('/manual-inventory/get-price/<sku_ids>/<print_type>')
@login_required
@cache.memoize(timeout=120)  # Cache for 2 minutes
def get_price(sku_ids, print_type):
    try:
        # Get user's currency from their settings
        user_currency = getattr(current_user, 'currency', 'USD')
        conversion_rate = CONVERSION_RATES.get(user_currency, 1.0)

        # Split the sku_ids string into a list
        sku_id_list = [skuid.strip() for skuid in sku_ids.split(',')]
        
        # Try to get prices from TCGPlayer API first
        tcgplayer_token = get_tcgplayer_token()
        if tcgplayer_token:
            try:
                headers = {
                    'Authorization': f'Bearer {tcgplayer_token}',
                    'Accept': 'application/json'
                }
                response = requests.get(
                    f'https://api.tcgplayer.com/pricing/sku/{",".join(sku_id_list)}',
                    headers=headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if 'results' in data:
                        # Process TCGPlayer prices
                        processed_prices = []
                        for result in data['results']:
                            skuId = str(result.get('skuId'))
                            lowPrice = result.get('lowPrice')
                            if lowPrice:
                                processed_prices.append({
                                    'skuId': skuId,
                                    'lowPrice': round(float(lowPrice) * conversion_rate, 2),
                                    'currency': user_currency
                                })
                        
                        if processed_prices:
                            return jsonify({
                                'success': True,
                                'prices': processed_prices,
                                'currency': user_currency
                            })
            except Exception as e:
                print(f"Error getting TCGPlayer prices: {str(e)}")
                # Continue to fallback to local prices
        
        # Fallback to local prices if TCGPlayer API fails or returns no results
        # Get catalog items to map skuIds to productIds for local price lookup
        catalog_items = list(mongo.db.catalog.find(
            {'skus.skuId': {'$in': [int(skuid) for skuid in sku_id_list]}},
            {'productId': 1, 'skus.skuId': 1}
        ))
        
        # Create mapping of skuId to productId
        product_mapping = {}
        for item in catalog_items:
            if item.get('skus'):
                for sku in item['skus']:
                    if str(sku.get('skuId')) in sku_id_list:
                        product_mapping[str(sku['skuId'])] = int(item['productId'])

        if product_mapping:
            # Get raw MongoDB collection
            prices_collection = mongo.db.prices
            
            pipeline = [
                # Match products
                {'$match': {'productId': {'$in': list(product_mapping.values())}}},
                
                # Filter by print type if specified
                *([{'$match': {'subTypeName': print_type}}] if print_type else []),
                
                # Unwind price fields
                {'$project': {
                    'productId': 1,
                    'subTypeName': 1,
                    'prices': {
                        '$objectToArray': {
                            'lowPrice': '$lowPrice',
                            'marketPrice': '$marketPrice',
                            'midPrice': '$midPrice'
                        }
                    }
                }},
                {'$unwind': '$prices'},
                
                # Filter out null/invalid prices
                {'$match': {
                    'prices.v': {'$ne': None},
                    'prices.v': {'$gt': 0}
                }},
                
                # Group by product to get first valid price
                {'$group': {
                    '_id': '$productId',
                    'price': {'$first': '$prices.v'}
                }},
                
                # Apply currency conversion
                {'$project': {
                    'productId': '$_id',
                    'lowPrice': {'$round': [{'$multiply': ['$price', conversion_rate]}, 2]},
                    'currency': {'$literal': user_currency}
                }}
            ]
            
            # Execute pipeline
            local_prices = list(prices_collection.aggregate(pipeline))
            
            # Convert product prices to sku prices
            reverse_mapping = {v: k for k, v in product_mapping.items()}
            processed_prices = []
            for price in local_prices:
                skuId = reverse_mapping.get(price['productId'])
                if skuId:
                    processed_prices.append({
                        'skuId': skuId,
                        'lowPrice': price['lowPrice'],
                        'currency': user_currency
                    })
            
            if processed_prices:
                return jsonify({
                    'success': True,
                    'prices': processed_prices,
                    'currency': user_currency
                })

        print(f"No prices found for skuIds: {sku_id_list}, print_type: {print_type}")
        return jsonify({
            'success': False,
            'error': 'Price not found in local database'
        })

    except Exception as e:
        print(f"Error getting prices: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })
