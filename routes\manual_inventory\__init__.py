from flask import Blueprint
from .ui_routes import ui_bp
from .filter_routes import filter_bp
from .catalog_routes import catalog_bp
from .price_routes import price_bp
from .inventory_routes import inventory_bp

# Create the main blueprint
manual_inventory_bp = Blueprint('manual_inventory', __name__)

# Register sub-blueprints
manual_inventory_bp.register_blueprint(ui_bp)
manual_inventory_bp.register_blueprint(filter_bp)
manual_inventory_bp.register_blueprint(catalog_bp)
manual_inventory_bp.register_blueprint(price_bp)
manual_inventory_bp.register_blueprint(inventory_bp)
