from typing import List, Dict, Any
from datetime import datetime
from bson import ObjectId
from pymongo import MongoClient

class Product:
    collection = None

    @classmethod
    def initialize_db(cls, mongo_uri, db_name):
        client = MongoClient(mongo_uri)
        db = client[db_name]
        cls.collection = db['catalog']

    def __init__(self, data: Dict[str, Any]):
        self._id: ObjectId = data.get('_id') if isinstance(data.get('_id'), ObjectId) else ObjectId(data.get('_id'))
        self.product_id: int = data.get('productId')
        self.abbreviation: str = data.get('abbreviation')
        self.category_id: int = data.get('categoryId')
        self.clean_name: str = data.get('cleanName')
        self.expansion_abbreviation: str = data.get('expansionAbbreviation')
        self.expansion_name: str = data.get('expansionName')
        self.extended_data: List[Dict[str, str]] = data.get('extendedData', [])
        self.game_abbreviation: str = data.get('gameAbbreviation')
        self.game_name: str = data.get('gameName')
        self.game_seo_name: str = data.get('gameSeoName')
        self.group_id: int = data.get('groupId')
        self.image: str = data.get('image')
        self.image_count: int = data.get('imageCount')
        self.image_url: str = data.get('imageUrl')
        self.modified_on: datetime = self._parse_date(data.get('modifiedOn'))
        self.name: str = data.get('name')
        self.number: str = data.get('number')
        self.presale_info: Dict[str, Any] = data.get('presaleInfo', {})
        self.prices: List[Dict[str, Any]] = data.get('prices', [])
        self.rarity: str = data.get('rarity')
        self.skus: List[Dict[str, Any]] = data.get('skus', [])
        self.subtype: str = data.get('subtype')
        self.url: str = data.get('url')
        self.us_prices: List[Dict[str, Any]] = data.get('usPrices', [])
        self.id_product: int = data.get('idProduct')
        self.is_sealed: bool = data.get('isSealed')
        self.is_single: bool = data.get('isSingle')
        self.prices_updated: datetime = self._parse_date(data.get('pricesUpdated'))
        self.blueprint_id: int = data.get('blueprint_id')
        self.price_increased: bool = data.get('priceIncreased')
        self.released_on: datetime = self._parse_date(data.get('releasedOn'))

    def _parse_date(self, date_value):
        if isinstance(date_value, datetime):
            return date_value
        elif isinstance(date_value, str):
            return datetime.fromisoformat(date_value)
        elif isinstance(date_value, dict) and '$date' in date_value:
            return datetime.fromisoformat(date_value['$date'])
        return None

    def get_lowest_price(self, condition: str = 'NM', printing: str = 'Normal') -> float:
        for price in self.prices:
            if price['subTypeName'] == printing:
                return price.get(f'lowPrice{condition}', 0.0)
        return 0.0

    def to_dict(self) -> Dict[str, Any]:
        return {
            'product_id': self.product_id,
            'name': self.name,
            'expansion': self.expansion_name,
            'game': self.game_name,
            'rarity': self.rarity,
            'lowest_price_nm': self.get_lowest_price('NM'),
            'lowest_price_lp': self.get_lowest_price('LP'),
            'lowest_price_mp': self.get_lowest_price('MP'),
            'lowest_price_hp': self.get_lowest_price('HP'),
            'lowest_price_dm': self.get_lowest_price('DM'),
            'image_url': self.image_url  # Add this line to include the image URL
        }

    @classmethod
    def get_unique_game_names(cls) -> List[str]:
        if cls.collection is None:
            raise ValueError("Database not initialized. Call initialize_db first.")
        try:
            unique_names = cls.collection.distinct('gameName')
            print(f"Fetched {len(unique_names)} unique game names")
            # Filter out None values and sort
            valid_names = [name for name in unique_names if name is not None]
            return sorted(valid_names)
        except Exception as e:
            print(f"Error fetching unique game names: {str(e)}")
            return []
