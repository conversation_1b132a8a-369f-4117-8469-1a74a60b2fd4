from datetime import datetime
from typing import Optional
from bson import ObjectId
from pymongo.database import Database
from pymongo.collection import Collection

class EbayAuthModel:
    def __init__(self, db: Database):
        self.collection: Collection = db['ebay_auth']
        self.setup_indexes()

    def setup_indexes(self):
        """Setup required indexes for the collection."""
        self.collection.create_index('user_id', unique=True)
        self.collection.create_index('token_expiry')

    def create_or_update(self, user_id: str, access_token: str, refresh_token: str, 
                        expires_in: int, scope: str) -> dict:
        """Create or update eBay authentication for a user."""
        now = datetime.utcnow()
        token_expiry = datetime.utcnow().timestamp() + expires_in

        auth_data = {
            'user_id': user_id,
            'access_token': access_token,
            'refresh_token': refresh_token,
            'token_expiry': token_expiry,
            'scope': scope,
            'updated_at': now
        }

        result = self.collection.update_one(
            {'user_id': user_id},
            {'$set': auth_data, '$setOnInsert': {'created_at': now}},
            upsert=True
        )

        auth_data['_id'] = result.upserted_id if result.upserted_id else \
                          self.collection.find_one({'user_id': user_id})['_id']
        return auth_data

    def get_by_user_id(self, user_id: str) -> Optional[dict]:
        """Get eBay authentication data for a user."""
        return self.collection.find_one({'user_id': user_id})

    def delete_by_user_id(self, user_id: str) -> bool:
        """Delete eBay authentication data for a user."""
        result = self.collection.delete_one({'user_id': user_id})
        return result.deleted_count > 0

    def update_tokens(self, user_id: str, access_token: str, 
                     expires_in: int) -> Optional[dict]:
        """Update access token and expiry for a user."""
        now = datetime.utcnow()
        token_expiry = now.timestamp() + expires_in

        result = self.collection.update_one(
            {'user_id': user_id},
            {
                '$set': {
                    'access_token': access_token,
                    'token_expiry': token_expiry,
                    'updated_at': now
                }
            }
        )

        if result.modified_count > 0:
            return self.get_by_user_id(user_id)
        return None

    def is_token_valid(self, user_id: str) -> bool:
        """Check if the user's access token is still valid."""
        auth_data = self.get_by_user_id(user_id)
        if not auth_data:
            return False
        
        now = datetime.utcnow().timestamp()
        return auth_data.get('token_expiry', 0) > now
