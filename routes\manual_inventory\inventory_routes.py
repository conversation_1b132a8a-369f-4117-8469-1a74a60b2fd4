from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from models.catalog_model import Catalog
from models.inventory_model import Inventory
from models.database import mongo
from datetime import datetime

inventory_bp = Blueprint('manual_inventory_inventory', __name__)

@inventory_bp.route('/api/add-to-inventory', methods=['POST'])
@login_required
def add_to_inventory():
    data = request.json
    prices_collection = mongo.db.prices
    
    # Check if this is a bulk save
    if isinstance(data, list):
        success_count = 0
        update_count = 0
        errors = []
        
        for item in data:
            if item.get('quantity', 0) <= 0:
                continue
                
            try:
                # Get the print type from the request
                print_type = request.args.get('printType', '')
                
                # Get catalog item, excluding price fields
                catalog_item = Catalog.objects(id=item['itemId']).only(
                    'id', 'name', 'skus', 'gameName', 'expansionName', 'number', 'productId', 'imageUrl'
                ).first()
                
                if not catalog_item:
                    errors.append(f"Item not found: {item['itemId']}")
                    continue
                
                # Filter SKUs by print type if specified
                filtered_skus = [s for s in catalog_item.skus if not print_type or s.printingName == print_type]
                sku = next((s for s in filtered_skus if str(s.skuId) == item['skuId']), None)
                if not sku:
                    errors.append(f"SKU not found for item: {catalog_item.name}")
                    continue
                
                # Check if item already exists in inventory with same print type
                existing_item = Inventory.objects(
                    userId=str(current_user.id),
                    skuId=str(sku.skuId),
                    printType=sku.printingName
                ).first()
                
                if existing_item:
                    # Update existing item
                    existing_item.quantity += item['quantity']
                    existing_item.lastModified = datetime.utcnow()
                    existing_item.needs_shopify_sync = True
                    existing_item.save()
                    update_count += 1
                else:
                    # Get price from local prices collection
                    price = 9999  # Default high price if we can't get a real price
                    try:
                        # Get all price documents for this product
                        price_docs = list(prices_collection.find({'productId': int(catalog_item.productId)}))
                        
                        # Get price for each print type
                        prices_by_sku = {}
                        for doc in price_docs:
                            if doc.get('subTypeName'):
                                for field in ['lowPrice', 'marketPrice', 'midPrice']:
                                    if field in doc and doc[field] not in (None, 'null', ''):
                                        try:
                                            value = float(doc[field])
                                            if value > 0:
                                                prices_by_sku[doc['subTypeName']] = value
                                                break
                                        except (ValueError, TypeError):
                                            continue
                        
                        # Use the price for this SKU's print type
                        if sku.printingName in prices_by_sku:
                            price = prices_by_sku[sku.printingName]
                    except Exception as e:
                        print(f"Error getting price for item {catalog_item.id}: {str(e)}")

                    # Create new item with price
                    inventory_item = Inventory(
                        userId=str(current_user.id),
                        username=current_user.username,
                        catalogId=str(catalog_item.id),
                        skuId=str(sku.skuId),
                        productId=str(catalog_item.productId),
                        name=catalog_item.name,
                        quantity=item['quantity'],
                        condition=sku.condAbbr,
                        gameName=catalog_item.gameName,
                        expansionName=catalog_item.expansionName,
                        number=catalog_item.number,
                        imageUrl=getattr(catalog_item, 'imageUrl', ''),
                        language=sku.langAbbr,
                        printType=sku.printingName,
                        price=price,
                        needs_shopify_sync=True
                    )
                    inventory_item.save()
                    success_count += 1
                
            except Exception as e:
                errors.append(f"Error processing {item.get('name', 'unknown item')}: {str(e)}")
        
        message = f'Added {success_count} new items'
        if update_count > 0:
            message += f' and updated {update_count} existing items'
        message += ' in inventory'
        
        return jsonify({
            'success': True,
            'message': message,
            'errors': errors if errors else None
        })
    
    # Single item save
    try:
        # Get the print type from the request
        print_type = request.args.get('printType', '')
        
        catalog_item = Catalog.objects(id=data['itemId']).only(
            'id', 'name', 'skus', 'gameName', 'expansionName', 'number', 'productId', 'imageUrl'
        ).first()
        
        if not catalog_item:
            return jsonify({'error': 'Item not found'}), 404
        
        # Filter SKUs by print type if specified
        filtered_skus = [s for s in catalog_item.skus if not print_type or s.printingName == print_type]
        sku = next((s for s in filtered_skus if str(s.skuId) == data['skuId']), None)
        if not sku:
            return jsonify({'error': 'SKU not found'}), 404
        
        # Check if item already exists in inventory with same print type
        existing_item = Inventory.objects(
            userId=str(current_user.id),
            skuId=str(sku.skuId),
            printType=sku.printingName
        ).first()
        
        if existing_item:
            # Update existing item
            existing_item.quantity += data['quantity']
            existing_item.lastModified = datetime.utcnow()
            existing_item.needs_shopify_sync = True
            existing_item.save()
            return jsonify({'success': True, 'message': 'Updated existing inventory item'})
        
        # Get price from local prices collection
        price = 9999  # Default high price if we can't get a real price
        try:
            # Get all price documents for this product
            price_docs = list(prices_collection.find({'productId': int(catalog_item.productId)}))
            
            # Get price for each print type
            prices_by_sku = {}
            for doc in price_docs:
                if doc.get('subTypeName'):
                    for field in ['lowPrice', 'marketPrice', 'midPrice']:
                        if field in doc and doc[field] not in (None, 'null', ''):
                            try:
                                value = float(doc[field])
                                if value > 0:
                                    prices_by_sku[doc['subTypeName']] = value
                                    break
                            except (ValueError, TypeError):
                                continue
            
            # Use the price for this SKU's print type
            if sku.printingName in prices_by_sku:
                price = prices_by_sku[sku.printingName]
        except Exception as e:
            print(f"Error getting price for item {catalog_item.id}: {str(e)}")

        inventory_item = Inventory(
            userId=str(current_user.id),
            username=current_user.username,
            catalogId=str(catalog_item.id),
            skuId=str(sku.skuId),
            productId=str(catalog_item.productId),
            name=catalog_item.name,
            quantity=data['quantity'],
            condition=sku.condAbbr,
            gameName=catalog_item.gameName,
            expansionName=catalog_item.expansionName,
            number=catalog_item.number,
            imageUrl=getattr(catalog_item, 'imageUrl', ''),
            language=sku.langAbbr,
            printType=sku.printingName,
            price=price,
            needs_shopify_sync=True
        )
        inventory_item.save()
        return jsonify({'success': True, 'message': 'Added new item to inventory'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
