from mongoengine import Document, <PERSON><PERSON>ield, DateTimeField, ReferenceField
from datetime import datetime

class EmailRecord(Document):
    username = StringField(required=True)
    to = StringField(required=True)
    cc = StringField()  # Add this line
    subject = StringField(required=True)
    text = StringField(required=True)
    status_code = StringField()
    mailgun_id = StringField()
    created_at = DateTimeField(default=datetime.utcnow)
