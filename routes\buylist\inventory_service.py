from flask import request, jsonify
from flask_login import login_required, current_user
from bson import ObjectId
from datetime import datetime
import logging
from routes.buylist import buylist_bp
from routes.buylist.core import logger, order_collection, inventory_collection, db

@buylist_bp.route('/api/send-to-staged/<order_id>', methods=['POST'])
@login_required
def send_to_staged(order_id):
    """
    Send an item from an order to the staged inventory.
    
    Args:
        order_id (str): The ID of the order
        
    Returns:
        JSON response with success or error message
    """
    try:
        data = request.json
        logger.info(f"Received data for send_to_staged: {data}")
        
        if not data:
            logger.error("No JSON data received")
            return jsonify({'success': False, 'error': 'No JSON data received'}), 400
        
        selected_sku_id = data.get('selectedSkuId')
        if not selected_sku_id:
            logger.error("selectedSkuId is missing from the request")
            return jsonify({'success': False, 'error': 'selectedSkuId is missing from the request'}), 400

        logger.info(f"Processing order_id: {order_id}, selected_sku_id: {selected_sku_id}")

        order = order_collection.find_one({'_id': ObjectId(order_id)})
        if not order:
            logger.error(f"Order not found: {order_id}")
            return jsonify({'success': False, 'error': 'Order not found'}), 404

        item_to_stage = next((item for item in order['line_items'] if str(item['skuId']) == str(selected_sku_id)), None)
        if not item_to_stage:
            logger.error(f"Selected SKU {selected_sku_id} not found in the order {order_id}")
            return jsonify({'success': False, 'error': 'Selected SKU not found in the order'}), 400

        if item_to_stage.get('processed'):
            logger.warning(f"Item with SKU {selected_sku_id} has already been processed")
            return jsonify({'success': False, 'error': 'This item has already been sent to inventory'}), 400

        logger.info(f"Item to stage: {item_to_stage}")

        # Query shProducts for the corresponding record
        sh_product = None
        if 'productId' in item_to_stage:
            sh_product = db.shProducts.find_one({
                "productId": item_to_stage['productId'],
                "username": current_user.username
            })

        # Match the card to the appropriate variant
        matched_variant = None
        if sh_product:
            card_condition = item_to_stage.get('condition', '').lower()
            card_foil = item_to_stage.get('foil', False)
            
            logger.info(f"Matching card: {item_to_stage['name']}, Condition: {card_condition}, Foil: {card_foil}")
            
            for variant in sh_product.get('variants', []):
                variant_title = variant.get('title', '').lower()
                logger.debug(f"Checking variant: {variant_title}")
                
                # Check if the variant matches the card's condition and foil status
                condition_match = (
                    (card_condition == 'nm' and ('near mint' in variant_title or variant_title.startswith('nm'))) or
                    (card_condition != 'nm' and card_condition in variant_title)
                )
                foil_match = (
                    (card_foil and 'foil' in variant_title) or
                    (not card_foil and 'foil' not in variant_title)
                )
                
                # Special case for non-foil Near Mint cards
                if card_condition == 'nm' and not card_foil and 'foil' not in variant_title:
                    condition_match = True
                
                logger.debug(f"Condition match: {condition_match}, Foil match: {foil_match}")
                
                if condition_match and foil_match:
                    matched_variant = variant
                    logger.info(f"Matched variant found: {variant_title}")
                    break
            
            if not matched_variant:
                logger.warning(f"No exact matching variant found for card: {item_to_stage['name']}. Attempting to find the closest match.")
                
                # If no exact match is found, try to find the closest match
                for variant in sh_product.get('variants', []):
                    variant_title = variant.get('title', '').lower()
                    if (card_foil == ('foil' in variant_title)) and ('near mint' in variant_title or variant_title.startswith('nm')):
                        matched_variant = variant
                        logger.info(f"Closest match found: {variant_title}")
                        break

        inventory_item = {
            "skuId": str(item_to_stage['skuId']),
            "username": current_user.username,
            "cardmarketId": "",
            "categoryId": "1",
            "comments": "",
            "condition": item_to_stage.get('condition', 'NM'),
            "foil": "Foil" if item_to_stage.get('foil', False) else "Normal",
            "lowPrice": float(item_to_stage.get('lowPrice', 0)),
            "name": item_to_stage['name'],
            "number": item_to_stage.get('number', ''),
            "quantity": item_to_stage.get('quantity', 1),
            "set": item_to_stage.get('set', ''),
            "setCode": item_to_stage.get('setCode', ''),
            "expansionName": item_to_stage.get('expansionName', ''),
            "updated_at": datetime.utcnow(),
            "productId": item_to_stage['productId'],
            "shProduct": {
                "id": sh_product.get('id') if sh_product else None,
                "title": sh_product.get('title') if sh_product else None,
                "variant": {
                    "id": matched_variant.get('id') if matched_variant else None,
                    "title": matched_variant.get('title') if matched_variant else None,
                    "sku": matched_variant.get('sku') if matched_variant else None,
                    "price": matched_variant.get('price') if matched_variant else None,
                    "inventory_quantity": matched_variant.get('inventory_quantity', 0) if matched_variant else None
                } if matched_variant else None
            } if sh_product else None,
            "matched_variant": bool(matched_variant)
        }
        inventory_result = inventory_collection.insert_one(inventory_item)
        logger.info(f"Inventory insert result: {inventory_result.inserted_id}")

        order_collection.update_one(
            {'_id': ObjectId(order_id), 'line_items.skuId': selected_sku_id},
            {'$set': {'line_items.$.processed': True}}
        )
        logger.info(f"Marked item with SKU {selected_sku_id} as processed in order {order_id}")

        return jsonify({'success': True, 'message': 'Item sent to inventory successfully'})

    except Exception as e:
        logger.error(f"Error sending item to inventory: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'An error occurred while processing the request: {str(e)}'}), 500

@buylist_bp.route('/api/send-all-to-staged/<order_id>', methods=['POST'])
@login_required
def send_all_to_staged(order_id):
    """
    Send all items from an order to the staged inventory.
    
    Args:
        order_id (str): The ID of the order
        
    Returns:
        JSON response with success or error message
    """
    try:
        order = order_collection.find_one({'_id': ObjectId(order_id)})
        if not order:
            return jsonify({'success': False, 'error': 'Order not found'}), 404

        # Get all unprocessed items
        unprocessed_items = [item for item in order.get('line_items', []) if not item.get('processed')]
        
        for item in unprocessed_items:
            # Query shProducts for the corresponding record
            sh_product = db.shProducts.find_one({
                "productId": item['productId'],
                "username": current_user.username
            })

            # Match the card to the appropriate variant
            matched_variant = None
            if sh_product:
                card_condition = item.get('condition', '').lower()
                card_foil = item.get('foil', False)
                
                for variant in sh_product.get('variants', []):
                    variant_title = variant.get('title', '').lower()
                    
                    condition_match = (
                        (card_condition == 'nm' and ('near mint' in variant_title or variant_title.startswith('nm'))) or
                        (card_condition != 'nm' and card_condition in variant_title)
                    )
                    foil_match = (
                        (card_foil and 'foil' in variant_title) or
                        (not card_foil and 'foil' not in variant_title)
                    )
                    
                    if condition_match and foil_match:
                        matched_variant = variant
                        break
                
                if not matched_variant:
                    for variant in sh_product.get('variants', []):
                        variant_title = variant.get('title', '').lower()
                        if (card_foil == ('foil' in variant_title)) and ('near mint' in variant_title or variant_title.startswith('nm')):
                            matched_variant = variant
                            break

            inventory_item = {
                "skuId": str(item['skuId']),
                "username": current_user.username,
                "cardmarketId": "",
                "categoryId": "1",
                "comments": "",
                "condition": item.get('condition', 'NM'),
                "foil": "Foil" if item.get('foil', False) else "Normal",
                "lowPrice": float(item.get('lowPrice', 0)),
                "name": item['name'],
                "number": item.get('number', ''),
                "quantity": item.get('quantity', 1),
                "set": item.get('set', ''),
                "setCode": item.get('setCode', ''),
                "expansionName": item.get('expansionName', ''),
                "updated_at": datetime.utcnow(),
                "productId": item['productId'],
                "shProduct": {
                    "id": sh_product.get('id') if sh_product else None,
                    "title": sh_product.get('title') if sh_product else None,
                    "variant": {
                        "id": matched_variant.get('id') if matched_variant else None,
                        "title": matched_variant.get('title') if matched_variant else None,
                        "sku": matched_variant.get('sku') if matched_variant else None,
                        "price": matched_variant.get('price') if matched_variant else None,
                        "inventory_quantity": matched_variant.get('inventory_quantity', 0) if matched_variant else None
                    } if matched_variant else None
                } if sh_product else None,
                "matched_variant": bool(matched_variant)
            }
            
            # Insert into inventory collection
            inventory_result = inventory_collection.insert_one(inventory_item)
            logger.info(f"Inventory insert result: {inventory_result.inserted_id}")

            # Mark the item as processed in the order
            order_collection.update_one(
                {'_id': ObjectId(order_id), 'line_items.skuId': item['skuId']},
                {'$set': {'line_items.$.processed': True}}
            )

        return jsonify({'success': True, 'message': 'All items sent to inventory successfully'})

    except Exception as e:
        logger.error(f"Error sending all items to inventory: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': 'An error occurred while processing the request'}), 500
