"""
Centralized MongoDB connection utility
This module provides access to the optimized MongoDB connection instance
"""
from optimized_mongo_connection import OptimizedMongoConnection

# Get the singleton instance of OptimizedMongoConnection
mongo_connection = OptimizedMongoConnection.get_instance()

def get_db():
    """Get the MongoEngine database connection"""
    return mongo_connection.db

def get_pymongo_db():
    """Get the PyMongo database connection"""
    return mongo_connection.pymongo_db

def get_connection():
    """Get the raw MongoDB connection"""
    return mongo_connection.get_connection()
