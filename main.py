import os
import sys
import json
import time
import logging
import requests
import atexit
import signal
from datetime import datetime, timedelta
import threading
import subprocess
from functools import wraps
from typing import Any, Callable, Dict, Optional, Tuple, Union

# Remove patch imports as we're now using MongoEngine directly

import redis
import dramatiq
from dramatiq_config import broker

from flask import Flask, redirect, url_for, request, render_template, render_template_string, abort, session, Blueprint, jsonify
from flask_login import Login<PERSON><PERSON><PERSON>, current_user, login_required, login_user
from werkzeug.security import check_password_hash
from flask_cors import CORS
from mongoengine.connection import ConnectionFailure
from bson import ObjectId
from pymongo.mongo_client import MongoClient
from cache_config import init_cache

# Import these within functions where needed to avoid circular imports
# from routes.auth_routes import send_email, is_production
from routes.admin_routes import ensure_user_subscription
from config import Config
from models.user_model import User
from models.user_settings_model import UserSettings
from utils.template_filters import register_template_filters
from optimized_mongo_connection import OptimizedMongoConnection
from routes.mobile_card_scanning_routes import mobile_card_scanning

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global variables for process management
dramatiq_process = None
webhook_server_process = None
shopify_sync_process = None
shopify_processor_process = None
webhook_worker_threads = []

def check_redis():
    """Check if Redis is running and accessible"""
    try:
        redis_client = redis.Redis(host='localhost', port=6379, db=0)
        redis_client.ping()
        logger.info("Redis is running")
        return True
    except redis.ConnectionError as e:
        logger.error(f"Redis is not running: {e}")
        return False

def start_dramatiq():
    """Start the Dramatiq worker process"""
    global dramatiq_process
    try:
        logger.info("Starting Dramatiq worker...")
        dramatiq_process = subprocess.Popen(
            [sys.executable, 'start_dramatiq.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        logger.info("Dramatiq worker started")
    except Exception as e:
        logger.error(f"Failed to start Dramatiq worker: {e}")
        raise

def start_webhook_server():
    """Start the webhook server process"""
    global webhook_server_process
    try:
        logger.info("Starting webhook server...")
        webhook_server_process = subprocess.Popen(
            [sys.executable, 'app.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        logger.info("Webhook server started")
    except Exception as e:
        logger.error(f"Failed to start webhook server: {e}")
        raise

def start_shopify_sync():
    """Start the Shopify inventory sync process"""
    global shopify_sync_process
    try:
        logger.info("Starting Shopify inventory sync process...")
        shopify_sync_process = subprocess.Popen(
            [sys.executable, 'shopify_sync.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        logger.info("Shopify inventory sync process started")
    except Exception as e:
        logger.error(f"Failed to start Shopify inventory sync process: {e}")
        raise

def start_shopify_processor():
    """Start the Shopify JSON processor process"""
    global shopify_processor_process
    try:
        logger.info("Starting Shopify JSON processor process...")
        shopify_processor_process = subprocess.Popen(
            [sys.executable, 'shopify_processor.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        logger.info("Shopify JSON processor process started")
    except Exception as e:
        logger.error(f"Failed to start Shopify JSON processor process: {e}")
        raise

def cleanup():
    """Cleanup function to be called on shutdown"""
    logger.info("Starting cleanup...")
    if dramatiq_process:
        logger.info("Stopping Dramatiq worker...")
        try:
            dramatiq_process.terminate()
            try:
                dramatiq_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                dramatiq_process.kill()
                dramatiq_process.wait()
        except Exception as e:
            logger.error(f"Error stopping Dramatiq worker: {e}")
        logger.info("Dramatiq worker stopped")

    if webhook_server_process:
        logger.info("Stopping webhook server...")
        try:
            webhook_server_process.terminate()
            try:
                webhook_server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                webhook_server_process.kill()
                webhook_server_process.wait()
        except Exception as e:
            logger.error(f"Error stopping webhook server: {e}")
        logger.info("Webhook server stopped")

    if shopify_sync_process:
        logger.info("Stopping Shopify inventory sync process...")
        try:
            shopify_sync_process.terminate()
            try:
                shopify_sync_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                shopify_sync_process.kill()
                shopify_sync_process.wait()
        except Exception as e:
            logger.error(f"Error stopping Shopify inventory sync process: {e}")
        logger.info("Shopify inventory sync process stopped")

    if shopify_processor_process:
        logger.info("Stopping Shopify JSON processor process...")
        try:
            shopify_processor_process.terminate()
            try:
                shopify_processor_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                shopify_processor_process.kill()
                shopify_processor_process.wait()
        except Exception as e:
            logger.error(f"Error stopping Shopify JSON processor process: {e}")
        logger.info("Shopify JSON processor process stopped")

# Register cleanup function to run on shutdown
atexit.register(cleanup)

# Handle SIGTERM gracefully
def handle_sigterm(signum, frame):
    logger.info("Received SIGTERM signal")
    cleanup()
    sys.exit(0)

signal.signal(signal.SIGTERM, handle_sigterm)

# Get MongoDB connection instance
mongo = OptimizedMongoConnection.get_instance()

# Ensure Redis is running
if not check_redis():
    logger.error("Redis is required but not running. Please start Redis first.")
    sys.exit(1)

# Background processes are disabled as per user request
# start_dramatiq()
# start_webhook_server()
# start_shopify_sync()
# start_shopify_processor()

# Start periodic Shopify tasks
def start_periodic_shopify_tasks():
    """Start the periodic Shopify tasks"""
    try:
        logger.info("================================================================")
        logger.info("STARTING SHOPIFY PUSH BACKGROUND TASK")
        logger.info("This will scan the shopify_json_files folder for files to process")
        logger.info("================================================================")

        from tasks.periodic_shopify_tasks import schedule_periodic_shopify_tasks

        # Schedule the first run immediately
        message_id = schedule_periodic_shopify_tasks.send()

        logger.info(f"Shopify push task scheduled with message ID: {message_id}")
        logger.info("The task will run immediately and then every 5 minutes")
        logger.info("================================================================")
    except Exception as e:
        logger.error(f"Failed to start periodic Shopify tasks: {e}")
        raise

# Periodic tasks are disabled as per user request
# logger.info("Starting background tasks...")
# start_periodic_shopify_tasks()

def get_location_from_ip(ip_address):
    try:
        response = requests.get(f"https://ipapi.co/{ip_address}/json/")
        data = response.json()
        location = f"{data.get('city', 'Unknown')} {data.get('region', 'Unknown')} {data.get('country_name', 'Unknown')}"
        return location
    except Exception as e:
        logger.error(f"Error getting location: {str(e)}")
        return "Location lookup failed"

class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder for handling MongoDB ObjectId and datetime objects."""
    def default(self, obj: Any) -> Any:
        if isinstance(obj, ObjectId):
            return str(obj)
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

class FlaskAppFactory:
    """Factory class for creating and configuring Flask application."""

    @staticmethod
    def create_app(config_class: type = Config) -> Flask:
        """Create and configure the Flask application."""
        app = Flask(__name__)
        FlaskAppFactory._configure_app(app, config_class)
        FlaskAppFactory._init_extensions(app)
        FlaskAppFactory._register_error_handlers(app)

        return app

    @staticmethod
    def _configure_app(app: Flask, config_class: type) -> None:
        """Configure Flask application settings."""
        app.config.from_object(config_class)

        # Session configuration
        session_config = {
            'PERMANENT_SESSION_LIFETIME': config_class.PERMANENT_SESSION_LIFETIME,
            'SESSION_COOKIE_SECURE': config_class.SESSION_COOKIE_SECURE,
            'SESSION_COOKIE_HTTPONLY': config_class.SESSION_COOKIE_HTTPONLY,
            'SESSION_COOKIE_SAMESITE': config_class.SESSION_COOKIE_SAMESITE,
            'SESSION_TYPE': config_class.SESSION_TYPE,
            'REMEMBER_COOKIE_DURATION': config_class.REMEMBER_COOKIE_DURATION,
            'REMEMBER_COOKIE_SECURE': config_class.REMEMBER_COOKIE_SECURE,
            'REMEMBER_COOKIE_HTTPONLY': config_class.REMEMBER_COOKIE_HTTPONLY,
            'REMEMBER_COOKIE_SAMESITE': config_class.REMEMBER_COOKIE_SAMESITE
        }
        app.config.update(session_config)

        # Register template filters
        register_template_filters(app)

        # Configure CORS - Allow all origins
        CORS(app, origins=["*"], resources={
            r"/*": {
                "origins": "*",  # Allow all origins
                "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
                "allow_headers": ["Authorization", "Content-Type", "X-Requested-With"],
                "expose_headers": ["Content-Type"]
            }
        })

        # Configure JSON handling for Flask 2.2.5
        from flask.json.provider import JSONProvider

        class CustomJSONProvider(JSONProvider):
            def dumps(self, obj, **kwargs):
                return json.dumps(obj, cls=CustomJSONEncoder, **kwargs)

            def loads(self, s, **kwargs):
                return json.loads(s, **kwargs)

        app.json = CustomJSONProvider(app)

    @staticmethod
    def _init_extensions(app: Flask) -> None:
        """Initialize Flask extensions."""
        # Initialize MongoDB
        mongo.init_app(app)

        # Initialize Login Manager
        login_manager = LoginManager()
        login_manager.init_app(app)
        login_manager.login_view = '/auth/login'  # Use direct path instead of endpoint
        login_manager.session_protection = 'strong'

        # Initialize Cache
        init_cache(app)

        @login_manager.user_loader
        def load_user(user_id: str) -> Optional[User]:
            return User.objects(pk=user_id).first()

    @staticmethod
    def _register_error_handlers(app: Flask) -> None:
        """Register error handlers for the application."""
        @app.errorhandler(404)
        def page_not_found(e: Exception) -> Tuple[str, int]:
            logger.error(f"404 error: {request.url}")
            return render_template('404.html'), 404

        @app.errorhandler(500)
        def internal_server_error(e: Exception) -> Tuple[str, int]:
            import traceback
            error_traceback = traceback.format_exc()
            logger.error(f"500 error: {str(e)}")
            logger.error(f"Traceback: {error_traceback}")

            # Check if it's an AJAX request
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'error': 'Internal Server Error',
                    'message': 'An unexpected error occurred. Please try clearing your cookies and refreshing the page.'
                }), 500

            # For regular requests, show a user-friendly error page
            return render_template('500.html', error_message="An unexpected error occurred. Please try clearing your cookies and refreshing the page."), 500

def admin_required(f: Callable) -> Callable:
    """Decorator to require admin privileges for a route."""
    @wraps(f)
    def decorated_function(*args: Any, **kwargs: Any) -> Any:
        if not current_user.is_authenticated or not current_user.is_admin:
            logger.error(f"Unauthorized admin access attempt by user: {getattr(current_user, 'username', 'anonymous')}")
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

def connect_to_mongodb() -> Optional[MongoClient]:
    """Establish connection to MongoDB with retry logic."""
    max_retries = 3
    retry_delay = 2  # seconds

    for attempt in range(max_retries):
        try:
            connection = mongo.get_connection()
            logger.info("Successfully connected to MongoDB")
            return connection
        except ConnectionFailure as e:
            logger.warning(f"MongoDB connection attempt {attempt+1} failed: {str(e)}")
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
            else:
                logger.error(f"Failed to connect to MongoDB server after {max_retries} attempts: {str(e)}")
                return None
        except Exception as e:
            logger.error(f"Unexpected error when connecting to MongoDB server: {str(e)}")
            return None

def register_blueprints(app: Flask, mongo_client: MongoClient) -> None:
    """Register all application blueprints."""
    from routes.warehouse import warehouse_bp
    from routes import (
        shopify_webhooks,
        auth_routes, profile_routes, integration_routes, orders_routes, shopify_orders_routes,
        image_recognition_routes, bored_routes, misc_routes, pos_routes, pos_payout_routes, dashboard_routes, card_routes, ebay_auth_routes,
        shopify_draft_orders,
        workflows, shopify_sync_routes, binder_transfer_routes,
        shopify_location_fix, warhammer_routes,
        mobile_uploads_routes, ebay_orders_routes, ebay_webhook_routes,
        cardmarket_routes, cardmarket_products_routes, cardmarket_prices_routes,
        cardmarket_order_management, board_games_routes, video_games_routes, other_routes, trollaus_routes,
        shopify_link_routes, custom_buylist_routes, ticket_routes,
        ebay_policies_routes, ebay_inventory_routes, ebay_settings_routes, ebay_shopify_conversion_routes,
        ebay_prefilled_listing_routes, ebay_inventory_delete_route, ebay_automation_routes,
        manual_inventory_routes, view_inventory_routes, seo_routes, reports_routes, onboarding_routes,
        shopify_products, shopify_bulk_edit_routes,
        shopify_update_catalog, shopify_add_inventory, shopify_cardmarket_routes,
        shopify_customers_routes, shopify_overview_routes, shopify_store_credit_routes,
        csv_routes, fbt_routes, global_sales_routes, cm_scanning_routes, admin_routes,
        tcgland_routes, cm_csv_routes, buylistrules_routes, customadd_routes,
        buylistsettings_routes, buylist_routes, shopify_export_routes, currency_routes,
        shopify_routes, activation_routes, card_scanning_routes, sports_card_routes,
        automations_routes, reporting_routes, user_settings_routes, ebay_routes,
        history_routes, card_details_routes, advanced_rules_routes, excluded_cards_routes,
        invoice_routes, cardmarket_api_routes, chatgpt_routes, shopify_autopricing, cardmarket_autopricing,
        cardmarket_research_routes, ebay_research_routes, downloads_routes, psa_checker_routes,
        stripe_webhook_routes, contact_routes, advanced_routes, eu_purchasing_routes,
        my_orders_routes, wallet_routes, bulk_cards_routes, cards_available_routes,
        cart_routes, biggest_gainers_routes, approve_item_routes, view_order_routes,
        cardmarket_syncing_routes, mobile_card_scanning_routes,
        reprice_logs_routes, pending_files_routes, card_grader_routes,
        favorites_routes, reprice_service_routes, kiosk_routes,
        shopify_standardizer_routes, upgrade_email_routes, enterprise_routes,
        inventory_routes, enterprise_test_routes, subscription_check_routes,
        events_routes, terminals_routes, receipt_routes, api_routes,
        shopify_inventory
    )

    # Import the product history routes
    from routes.history_routes import history_bp

    # Import premium routes for handling premium feature orders
    from routes.premium_routes import premium_bp

    # Import zoom routes for handling Zoom call scheduling
    from routes.zoom_routes import zoom_bp

    # Import free design routes for handling free design requests
    from routes.free_design_routes import free_design_bp

    # Import staff management routes - using new implementation with staff_members array
    from routes import new_staff_management as staff_management

    # Import staff routes - using new implementation with staff_members array
    from staff.routes.new_auth_routes import staff_auth_bp, register_staff_routes

    blueprints: Dict[str, Tuple[Union[Blueprint, Callable[[], Blueprint]], Optional[str]]] = {
        'shopify_webhooks': (shopify_webhooks.shopify_webhook_bp, '/shopify'),
        'auth': (lambda: auth_routes.auth_bp, '/auth'),  # Register with explicit URL prefix
        'enterprise': (enterprise_routes.enterprise_bp, '/enterprise'),
        'enterprise_test': (enterprise_test_routes.enterprise_test_bp, None),
        'subscription_check': (subscription_check_routes.subscription_check_bp, None),
        'binder_transfer': (binder_transfer_routes.binder_transfer_bp, None),
        'events': (events_routes.events, None),
        'api': (api_routes.api_bp, None),  # Direct API endpoints
        'shopify_standardizer': (shopify_standardizer_routes.standardizer_bp, None),
        # 'staff_auth' is registered separately via register_staff_routes(app)
        'profile': (profile_routes.profile_bp, None),
        'integration': (integration_routes.integration_bp, None),
        'orders': (lambda: orders_routes.create_orders_bp(mongo_client), None),
        'bored': (bored_routes.bored_bp, None),
        'misc': (misc_routes.misc_bp, None),
        'pos': (lambda: pos_routes.create_pos_bp(), None),
        'pos_payout': (pos_payout_routes.pos_payout_bp, None),
        'kiosk': (lambda: kiosk_routes.create_kiosk_bp(), None),
        'upgrade_email': (upgrade_email_routes.upgrade_email_bp, None),
        'shopify_products': (shopify_products.products_bp, None),
        'shopify_bulk_edit': (shopify_bulk_edit_routes.shopify_bulk_edit_bp, None),
        'shopify_sync': (shopify_sync_routes.bp, None),
        'shopify_autopricing': (lambda: shopify_autopricing.create_autopricing_bp(), None),
        'shopify_update_catalog': (lambda: shopify_update_catalog.create_update_catalog_bp(mongo_client), None),
        'shopify_cardmarket': (lambda: shopify_cardmarket_routes.create_shopify_cardmarket_bp(mongo_client), None),
        'cardmarket_syncing': (lambda: cardmarket_syncing_routes.create_cardmarket_syncing_bp(mongo_client), None),
        'shopify_add_inventory': (lambda: shopify_add_inventory.create_inventory_bp(mongo_client), None),
        'shopify_customers': (lambda: shopify_customers_routes.create_customers_bp(mongo_client), None),
        'shopify_orders': (lambda: shopify_orders_routes.create_orders_bp(mongo_client), None),
        'dashboard': (dashboard_routes.dashboard_bp, None),
        'card': (lambda: card_routes.create_card_bp(), None),
        'cardmarket': (cardmarket_routes.cardmarket_bp, None),
        'cardmarket_products': (cardmarket_products_routes.cardmarket_products_bp, None),
        'cardmarket_prices': (cardmarket_prices_routes.cardmarket_prices_bp, None),
        'cardmarket_order_management': (cardmarket_order_management.cardmarket_order_management_bp, None),
        'cardmarket_research': (cardmarket_research_routes.cardmarket_research_bp, None),
        'cardmarket_autopricing': (lambda: cardmarket_autopricing.create_cardmarket_autopricing_bp(), None),
        'shopify_link': (shopify_link_routes.shopify_link_bp, None),
        'ebay_research': (ebay_research_routes.ebay_research_bp, None),
        'downloads': (downloads_routes.downloads_bp, None),
        'psa_checker': (psa_checker_routes.psa_checker_bp, None),
        'reports': (reports_routes.reports_bp, None),
        'onboarding': (onboarding_routes.onboarding_bp, None),
        'csv': (csv_routes.csv_bp, '/csv'),
        'fbt': (fbt_routes.fbt_bp, None),
        'shopify_overview': (shopify_overview_routes.shopify_overview_bp, None),
        'shopify_store_credit': (lambda: shopify_store_credit_routes.create_store_credit_bp(), None),
        'shopify_draft_orders': (shopify_draft_orders.shopify_draft_orders_bp, None),
        'global_sales': (global_sales_routes.global_sales_bp, None),
        'cm_scanning': (cm_scanning_routes.cm_scanning_bp, None),
        'admin': (admin_routes.admin_bp, None),
        'tcgland': (tcgland_routes.tcgland_bp, None),
        'cm_csv': (cm_csv_routes.cm_csv_bp, '/cm_csv'),
        'buylistrules': (buylistrules_routes.buylistrules_bp, None),
        'buylist': (lambda: buylist_routes.create_buylist_bp(), '/buylist'),
        'customadd': (customadd_routes.customadd_bp, '/shopify'),
        'buylistsettings': (lambda: buylistsettings_routes.create_buylist_settings_bp(), '/buylist/settings'),
        'shopify_export': (shopify_export_routes.shopify_export_bp, None),
        'currency': (currency_routes.currency_bp, '/currency'),
        'shopify': (shopify_routes.shopify_bp, '/shopify'),
        'activation': (activation_routes.activation_bp, None),
        'card_scanning': (card_scanning_routes.card_scanning_bp, '/card_scanning'),
        'sports_card': (sports_card_routes.sports_card_scanning_bp, '/sports_card_scanning'),
        'automations': (automations_routes.automations_bp, None),
        'reporting': (reporting_routes.reporting_bp, None),
        'user_settings': (user_settings_routes.user_settings, None),
        'ebay': (ebay_routes.ebay_bp, '/ebay'),
        'history': (history_routes.history_bp, None),
        'card_details': (card_details_routes.card_details_bp, None),
        'advanced_rules': (advanced_rules_routes.advanced_rules, '/advanced_rules'),
        'excluded_cards': (excluded_cards_routes.excluded_cards, '/excluded_cards'),
        'invoice': (invoice_routes.invoices_bp, '/invoices'),
        'cardmarket_api': (cardmarket_api_routes.cardmarket_api_bp, '/cardmarket_api'),
        'chatgpt': (chatgpt_routes.chatgpt_routes, '/chatgpt'),
        'stripe_webhook': (stripe_webhook_routes.stripe_webhook_bp, None),
        'contact': (contact_routes.contact_bp, None),
        'advanced': (advanced_routes.advanced, None),
        'eu_purchasing': (eu_purchasing_routes.eu_purchasing_bp, None),
        'my_orders': (my_orders_routes.my_orders_bp, None),
        'wallet': (wallet_routes.wallet_bp, None),
        'bulk_cards': (bulk_cards_routes.bulk_cards_bp, None),
        'cards_available': (cards_available_routes.cards_available_bp, None),
        'cart': (cart_routes.cart_bp, None),
        'biggest_gainers': (biggest_gainers_routes.biggest_gainers_bp, None),
        'approve_item': (lambda: approve_item_routes.create_approve_item_bp(mongo_client), None),
        'view_order': (lambda: view_order_routes.create_view_order_bp(mongo_client), None),
        'warehouse': (warehouse_bp, None),
        'manual_inventory': (manual_inventory_routes.manual_inventory_bp, None),
        'view_inventory': (view_inventory_routes.view_inventory_bp, None),
        'seo': (seo_routes.seo_bp, None),
        'mobile_card_scanning': (mobile_card_scanning, None),
        'board_games': (board_games_routes.board_games_bp, None),
        'other': (other_routes.other_bp, None),
        'warhammer': (warhammer_routes.warhammer_bp, None),
        'video_games': (video_games_routes.video_games_bp, None),
        'ebay_auth': (ebay_auth_routes.ebay_auth_bp, None),  # Allow both /api/ebay/auth and /cardmarket/api/ebay/auth
        'mobile_uploads': (mobile_uploads_routes.mobile_uploads_bp, None),
        'ebay_orders': (ebay_orders_routes.ebay_orders_bp, None),
        'ebay_policies': (ebay_policies_routes.ebay_policies_bp, None),
        'ebay_inventory': (ebay_inventory_routes.ebay_inventory_bp, None),
        'ebay_settings': (ebay_settings_routes.ebay_settings, None),
        'ebay_webhook': (ebay_webhook_routes.ebay_webhook_bp, None),
        'ebay_shopify_conversion': (ebay_shopify_conversion_routes.ebay_shopify_conversion, None),
        'ebay_prefilled': (ebay_prefilled_listing_routes.ebay_prefilled, None),
        'ebay_prefilled_new': (lambda: __import__('routes.ebay_prefilled_listing_routes_new', fromlist=['ebay_prefilled_new']).ebay_prefilled_new, None),
        'ebay_inventory_delete': (ebay_inventory_delete_route.ebay_inventory_delete, None),
        'ebay_automation': (ebay_automation_routes.ebay_automation, '/ebay'),
        'image_recognition': (image_recognition_routes.image_recognition_bp, None),
        'shopify_location_fix': (shopify_location_fix.shopify_location_fix, None),
        'workflows': (workflows.workflows, None),
        'reprice_logs': (reprice_logs_routes.reprice_logs, None),
        'pending_files': (lambda: pending_files_routes.create_pending_files_bp(), None),
        'card_grader': (lambda: card_grader_routes.card_grader_bp, None),
        'custom_buylist': (custom_buylist_routes.custom_buylist_bp, None),
        'staff_management': (staff_management.staff_management_bp, None),
        'favorites': (favorites_routes.favorites_bp, None),
        'reprice_service': (reprice_service_routes.reprice_service, None),
        'cardtrader_repricer': (lambda: __import__('routes.cardtrader_repricer_routes', fromlist=['cardtrader_repricer_bp']).cardtrader_repricer_bp, None),
        'hotlist': (lambda: __import__('routes.buylist.hotlist_routes', fromlist=['hotlist_bp']).hotlist_bp, None),
        'ticket': (ticket_routes.ticket_bp, None),
        'inventory': (inventory_routes.inventory_bp, None),
        'premium': (premium_bp, None),  # Add premium routes for handling premium feature orders
        'zoom': (zoom_bp, None),  # Add zoom routes for handling Zoom call scheduling
        'free_design': (free_design_bp, None),  # Add free design routes for handling free design requests
        'terminals': (terminals_routes.terminals_bp, None),  # Add terminals routes for in-store terminal configuration
        'receipt': (lambda: receipt_routes.create_receipt_routes(mongo_client), None),  # Add receipt routes for POS receipt printing
        'trollaus': (trollaus_routes.trollaus_bp, None),  # Add TrollAus routes for handling purchased inventory
        'shopify_inventory': (shopify_inventory.shopify_inventory_bp, None),  # Add Shopify inventory routes for adjusting inventory
        'sales': (lambda: __import__('routes.sales_routes', fromlist=['sales_bp']).sales_bp, None),  # Add sales management routes for POS transaction management
        'pricing': (lambda: __import__('pricing', fromlist=['pricing_bp']).pricing_bp, None)  # Add pricing management routes
    }

    # Import additional routes
    from routes import shopify_location_fix, ebay_webhook_routes

    for name, (blueprint_or_factory, url_prefix) in blueprints.items():
        try:
            blueprint = blueprint_or_factory() if callable(blueprint_or_factory) else blueprint_or_factory
            app.register_blueprint(blueprint, url_prefix=url_prefix)

        except Exception as e:
            logger.error(f"Error registering blueprint '{name}': {str(e)}")
            raise

    # Register staff routes with custom static folder
    register_staff_routes(app)

def create_app(config_class: type = Config) -> Flask:
    """Create and configure the Flask application."""
    app = FlaskAppFactory.create_app(config_class)

    @app.before_request
    def make_session_permanent() -> None:
        session.permanent = True

    # Apply session recovery middleware to handle corrupted sessions
    from middlewares.session_middleware import session_recovery_middleware
    session_recovery_middleware(app)

    # Apply enterprise subscription check middleware to all routes
    from middlewares.enterprise_middleware import enterprise_subscription_required

    @app.before_request
    @enterprise_subscription_required
    def check_enterprise_subscription() -> None:
        # This function body is empty because the middleware handles everything
        pass

    # Initialize application with MongoDB connection
    with app.app_context():
        # Initialize Dramatiq broker
        dramatiq.set_broker(broker)

        # Try to establish MongoDB connection
        mongo_connection = connect_to_mongodb()

        # Store the MongoDB connection in the app context for reuse
        if mongo_connection:
            app.mongo_client = mongo_connection

        # Register all blueprints - even if MongoDB is down, we'll register the blueprints
        # so that routes that don't require MongoDB can still function
        try:
            register_blueprints(app, mongo_connection)
            logger.info("All blueprints registered successfully")
        except Exception as e:
            logger.error(f"Error registering blueprints: {e}")
            # Continue with partial functionality rather than crashing completely

        # Webhook worker threads are intentionally disabled to prevent webhook records from being deleted
        logger.info("Shopify webhook worker threads are disabled to preserve webhook records")

        # Add shutdown handler
        @app.teardown_appcontext
        def shutdown_session(exception=None):
            cleanup()

        # Add root route to redirect to login
        @app.route('/', methods=['GET', 'POST'])
        def index():
            if current_user.is_authenticated:
                return redirect(url_for('dashboard.dashboard'))
            # Redirect directly to /auth/login instead of using url_for
            return redirect('/auth/login')

        # Add /login route to redirect to /auth/login
        @app.route('/login', methods=['GET', 'POST'])
        def root_login_redirect():
            # Redirect directly to /auth/login instead of using url_for
            return redirect('/auth/login')

        # Direct staff login route
        @app.route('/direct-staff-login', methods=['GET'])
        def direct_staff_login():
            """Direct route to staff login page that bypasses blueprint template resolution"""
            import os
            staff_login_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'staff', 'templates', 'login.html')
            with open(staff_login_path, 'r') as f:
                template_content = f.read()
            return render_template_string(template_content)

        # Direct staff dashboard route for testing
        @app.route('/direct-staff-dashboard', methods=['GET'])
        def direct_staff_dashboard():
            """Direct route to staff dashboard page for testing"""
            import os
            from models.user_model import Staff

            # Create placeholder stats and activities
            stats = {
                'todays_orders': 0,
                'pending_shipments': 0,
                'inventory_count': 0,
                'buylist_offers': 0
            }
            activities = []

            # Get the dashboard template
            staff_dashboard_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'staff', 'templates', 'dashboard.html')
            with open(staff_dashboard_path, 'r') as f:
                template_content = f.read()

            # Render the template with the stats and activities
            return render_template_string(template_content, stats=stats, activities=activities, current_user=None)

        # Add a health check endpoint that doesn't require MongoDB
        @app.route('/health', methods=['GET'])
        def health_check():
            health_status = {
                "status": "ok",
                "mongodb_connected": mongo_connection is not None,
                "redis_connected": check_redis(),
                "timestamp": datetime.now().isoformat()
            }
            return jsonify(health_status)

    return app

# Create the application instance
app = create_app()

# For gunicorn
application = app

if __name__ == '__main__':
    # Run the application
    app.run(debug=True, host='0.0.0.0', port=8000)
