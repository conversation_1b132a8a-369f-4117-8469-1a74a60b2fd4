#!/usr/bin/env python3
import redis
import sys

try:
    # Connect to Redis
    r = redis.Redis(host='localhost', port=6379, password='RedisPassword123', decode_responses=True)
    
    # Test connection
    if r.ping():
        print("Successfully connected to Red<PERSON>!")
        
        # Set a test value
        r.set('test_key', 'Hello from Python')
        
        # Get the test value
        value = r.get('test_key')
        print(f"Retrieved test value: {value}")
        
        # Clean up
        r.delete('test_key')
        print("Test completed successfully!")
    else:
        print("Failed to ping Redis server.")
except Exception as e:
    print(f"Error connecting to Redis: {e}")
    sys.exit(1)

