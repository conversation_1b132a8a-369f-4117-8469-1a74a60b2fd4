from mongoengine import Document, <PERSON><PERSON>ield, ListField, DateTimeField, DecimalField, DictField, signals
from datetime import datetime

class EbayAccount(Document):
    user_id = StringField(required=True)
    access_token = StringField(required=True)
    refresh_token = StringField(required=True)
    token_expiry = DateTimeField(required=True)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'ebay_accounts',
        'indexes': [
            'user_id',
            ('user_id', 'access_token'),
        ]
    }

    def update_tokens(self, access_token, refresh_token, expires_in):
        """Update the account's tokens and expiry time."""
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.token_expiry = datetime.utcnow() + timedelta(seconds=expires_in)
        self.updated_at = datetime.utcnow()
        self.save()

    def is_token_valid(self):
        """Check if the access token is still valid."""
        return datetime.utcnow() < self.token_expiry

    def save(self, *args, **kwargs):
        """Update the updated_at field on save."""
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)

class EbayOrder(Document):
    order_id = StringField(required=True, unique=True)
    user_id = StringField(required=True)
    buyer_username = StringField()
    order_total = DecimalField()
    currency = StringField()
    order_status = StringField()
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    items = ListField(DictField())
    shipping_address = DictField()
    
    meta = {
        'collection': 'ebay_orders',
        'indexes': [
            'order_id',
            'user_id',
            ('user_id', 'order_id'),
            'created_at'
        ]
    }

    def save(self, *args, **kwargs):
        """Update the updated_at field on save."""
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)
