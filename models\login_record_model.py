from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Field, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ield
from models.user_model import User
import datetime

class LoginRecord(Document):
    user = ReferenceField(User)  # Optional now since failed logins might not have a valid user
    username = <PERSON><PERSON><PERSON>(required=True)  # Store attempted username
    timestamp = DateTimeField(default=datetime.datetime.utcnow)
    ip_address = StringField(required=True)
    location = StringField()
    success = BooleanField(required=True)  # Whether login was successful
    error_message = StringField()  # Error message for failed attempts
    
    meta = {
        'collection': 'login_records',
        'indexes': [
            'user',
            'username',
            'timestamp',
            '-timestamp',  # Index for sorting by most recent
            'success'  # Index for querying successful/failed attempts
        ]
    }
