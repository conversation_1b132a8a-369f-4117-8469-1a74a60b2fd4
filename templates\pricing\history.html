{% extends "base.html" %}

{% block title %}Pricing History{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-history me-2" style="color: #009688;"></i>
                Pricing History
            </h1>
            <p class="text-muted mb-0">View all pricing changes and track price movements</p>
        </div>
        <div>
            <a href="{{ url_for('pricing.pricing_dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="product_search" class="form-label">Product Name</label>
                    <input type="text" class="form-control" id="product_search" name="product_search" 
                           value="{{ request.args.get('product_search', '') }}" placeholder="Search products...">
                </div>
                <div class="col-md-2">
                    <label for="vendor_filter" class="form-label">Vendor</label>
                    <input type="text" class="form-control" id="vendor_filter" name="vendor_filter" 
                           value="{{ request.args.get('vendor_filter', '') }}" placeholder="Filter by vendor...">
                </div>
                <div class="col-md-2">
                    <label for="change_reason" class="form-label">Reason</label>
                    <select class="form-select" id="change_reason" name="change_reason">
                        <option value="">All reasons</option>
                        <option value="manual_update" {% if request.args.get('change_reason') == 'manual_update' %}selected{% endif %}>Manual Update</option>
                        <option value="bulk_update" {% if request.args.get('change_reason') == 'bulk_update' %}selected{% endif %}>Bulk Update</option>
                        <option value="pricing_rule" {% if request.args.get('change_reason') == 'pricing_rule' %}selected{% endif %}>Pricing Rule</option>
                        <option value="competitive_adjustment" {% if request.args.get('change_reason') == 'competitive_adjustment' %}selected{% endif %}>Competitive Adjustment</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="{{ request.args.get('date_from', '') }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="{{ request.args.get('date_to', '') }}">
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- History Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Pricing Changes</h6>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-download me-1"></i>
                    Export
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportHistory('csv')">Export as CSV</a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportHistory('excel')">Export as Excel</a></li>
                </ul>
            </div>
        </div>
        <div class="card-body">
            {% if history %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                    <thead class="table-light">
                        <tr>
                            <th>Date & Time</th>
                            <th>Product</th>
                            <th>Vendor</th>
                            <th>Old Price</th>
                            <th>New Price</th>
                            <th>Change</th>
                            <th>Change %</th>
                            <th>Reason</th>
                            <th>Updated By</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in history %}
                        <tr>
                            <td>
                                {% if record.updated_at %}
                                {{ record.updated_at.strftime('%Y-%m-%d') }}<br>
                                <small class="text-muted">{{ record.updated_at.strftime('%H:%M:%S') }}</small>
                                {% else %}
                                <span class="text-muted">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ record.product_name or 'Unknown Product' }}</strong>
                                {% if record.product_id %}
                                <br><small class="text-muted">ID: {{ record.product_id }}</small>
                                {% endif %}
                            </td>
                            <td>{{ record.vendor or 'N/A' }}</td>
                            <td>
                                <span class="text-muted">£{{ "%.2f"|format(record.old_price) }}</span>
                            </td>
                            <td>
                                <strong>£{{ "%.2f"|format(record.new_price) }}</strong>
                            </td>
                            <td>
                                {% set change = record.new_price - record.old_price %}
                                {% if change > 0 %}
                                    <span class="text-success fw-bold">+£{{ "%.2f"|format(change) }}</span>
                                {% elif change < 0 %}
                                    <span class="text-danger fw-bold">£{{ "%.2f"|format(change) }}</span>
                                {% else %}
                                    <span class="text-muted">£0.00</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if record.old_price > 0 %}
                                    {% set change_pct = ((record.new_price - record.old_price) / record.old_price) * 100 %}
                                    {% if change_pct > 0 %}
                                        <span class="text-success">+{{ "%.1f"|format(change_pct) }}%</span>
                                    {% elif change_pct < 0 %}
                                        <span class="text-danger">{{ "%.1f"|format(change_pct) }}%</span>
                                    {% else %}
                                        <span class="text-muted">0.0%</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if record.change_reason %}
                                    <span class="badge bg-secondary">{{ record.change_reason.replace('_', ' ').title() }}</span>
                                {% endif %}
                                {% if record.change_description %}
                                    <br><small class="text-muted">{{ record.change_description }}</small>
                                {% endif %}
                                {% if record.applied_rule_name %}
                                    <br><small class="text-info">Rule: {{ record.applied_rule_name }}</small>
                                {% endif %}
                            </td>
                            <td>{{ record.updated_by }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if pagination %}
            <nav aria-label="Pricing history pagination">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('pricing.pricing_history', page=pagination.prev_num, **request.args) }}">
                            <i class="fas fa-chevron-left"></i> Previous
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link"><i class="fas fa-chevron-left"></i> Previous</span>
                    </li>
                    {% endif %}

                    {% for page_num in range(1, (pagination.total // pagination.per_page) + 2) %}
                        {% if page_num <= 5 or page_num > (pagination.total // pagination.per_page) - 2 or (page_num >= pagination.page - 2 and page_num <= pagination.page + 2) %}
                            {% if page_num == pagination.page %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('pricing.pricing_history', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% endif %}
                        {% elif page_num == 6 or page_num == (pagination.total // pagination.per_page) - 2 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('pricing.pricing_history', page=pagination.next_num, **request.args) }}">
                            Next <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">Next <i class="fas fa-chevron-right"></i></span>
                    </li>
                    {% endif %}
                </ul>
            </nav>

            <div class="text-center text-muted">
                Showing {{ ((pagination.page - 1) * pagination.per_page) + 1 }} to 
                {{ pagination.page * pagination.per_page if pagination.page * pagination.per_page < pagination.total else pagination.total }} 
                of {{ pagination.total }} entries
            </div>
            {% endif %}

            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-history fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-muted">No Pricing History Found</h5>
                <p class="text-muted">No pricing changes match your current filters.</p>
                <a href="{{ url_for('pricing.pricing_history') }}" class="btn btn-outline-primary">
                    <i class="fas fa-refresh me-1"></i>
                    Clear Filters
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Summary Statistics -->
    {% if history %}
    <div class="row">
        <div class="col-md-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Changes
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if pagination %}{{ pagination.total }}{% else %}{{ history|length }}{% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-edit fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Price Increases
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ history|selectattr('new_price', 'gt', history|map(attribute='old_price'))|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Price Decreases
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ history|selectattr('new_price', 'lt', history|map(attribute='old_price'))|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Unique Products
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ history|map(attribute='product_id')|unique|list|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function exportHistory(format) {
    // Get current filter parameters
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    // Create download link
    const exportUrl = `{{ url_for('pricing.pricing_history') }}?${params.toString()}`;
    
    // For now, just show an alert - in a real implementation, this would trigger a download
    alert(`Export as ${format.toUpperCase()} functionality would be implemented here.\nURL: ${exportUrl}`);
}

// Auto-submit form when date fields change
document.getElementById('date_from').addEventListener('change', function() {
    if (this.value && document.getElementById('date_to').value) {
        this.form.submit();
    }
});

document.getElementById('date_to').addEventListener('change', function() {
    if (this.value && document.getElementById('date_from').value) {
        this.form.submit();
    }
});
</script>

{% endblock %}
