from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from models.catalog_model import Catalog
from models.database import mongo
from cache_config import cache
import requests
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor

# Hardcoded conversion rates (USD to other currencies)
CONVERSION_RATES = {
    'USD': 1.0,
    'EUR': 0.92,  # 1 USD = 0.92 EUR
    'GBP': 0.79,  # 1 USD = 0.79 GBP
    'CAD': 1.35,  # 1 USD = 1.35 CAD
    'AUD': 1.52,  # 1 USD = 1.52 AUD
    'JPY': 148.0, # 1 USD = 148 JPY
    'MXN': 17.15  # 1 USD = 17.15 MXN
}

catalog_bp = Blueprint('manual_inventory_catalog', __name__)

@catalog_bp.route('/api/catalog/barcode/<barcode>')
@login_required
def search_by_barcode(barcode):
    # Search for item with matching barcode
    query = {'barcode': barcode}
    
    pipeline = [
        {'$match': query},
        {'$lookup': {
            'from': 'inventory',
            'let': {'skuIds': '$skus.skuId'},
            'pipeline': [
                {'$match': {
                    '$expr': {
                        '$and': [
                            {'$eq': ['$userId', str(current_user.id)]},
                            {'$in': [{'$toInt': '$skuId'}, '$$skuIds']}
                        ]
                    }
                }},
                {'$project': {'skuId': 1, 'quantity': 1, '_id': 0}}
            ],
            'as': 'inventory'
        }},
        {'$project': {
            'id': {'$toString': '$_id'},
            'name': 1,
            'number': 1,
            'productId': {'$toString': '$productId'},
            'imageUrl': 1,
            'expansionName': {'$ifNull': ['$expansionName', 'N/A']},
            'rarity': {
                '$let': {
                    'vars': {
                        'rarityData': {
                            '$filter': {
                                'input': '$extendedData',
                                'as': 'data',
                                'cond': {'$eq': ['$$data.name', 'Rarity']}
                            }
                        }
                    },
                    'in': {
                        '$ifNull': [
                            {'$arrayElemAt': ['$$rarityData.value', 0]},
                            ''
                        ]
                    }
                }
            },
            'skus': 1,
            'variations': {
                '$map': {
                    'input': '$skus',
                    'as': 'sku',
                    'in': {
                        'printType': '$$sku.printingName',
                        'condition': '$$sku.condAbbr',
                        'language': '$$sku.langAbbr',
                        'skuId': '$$sku.skuId',
                        'currentQuantity': {
                            '$ifNull': [
                                {'$arrayElemAt': ['$inventory.quantity', 0]},
                                0
                            ]
                        }
                    }
                }
            },
            'skuMapping': {
                '$arrayToObject': {
                    '$map': {
                        'input': '$skus',
                        'as': 'sku',
                        'in': {
                            'k': {
                                '$concat': [
                                    '$$sku.printingName', '_',
                                    '$$sku.condAbbr', '_',
                                    '$$sku.langAbbr'
                                ]
                            },
                            'v': '$$sku.skuId'
                        }
                    }
                }
            }
        }}
    ]
    
    result = list(mongo.db.catalog.aggregate(pipeline))
    if not result:
        return jsonify([])
        
    # Get prices for the found item
    item = result[0]
    sku_ids = [str(sku.get('skuId')) for sku in item.get('skus', [])]
    
    prices = {}
    if sku_ids:
        tcgplayer_token = get_tcgplayer_token()
        if tcgplayer_token:
            try:
                headers = {
                    'Authorization': f'Bearer {tcgplayer_token}',
                    'Accept': 'application/json'
                }
                url = f'https://api.tcgplayer.com/pricing/sku/{",".join(sku_ids)}'
                response = requests.get(url, headers=headers)
                if response.status_code == 200:
                    data = response.json()
                    if 'results' in data:
                        for price_data in data['results']:
                            sku_id = str(price_data.get('skuId'))
                            low_price = price_data.get('lowPrice')
                            if low_price:
                                prices[sku_id] = float(low_price)
            except Exception as e:
                print(f"Error getting TCGPlayer prices: {str(e)}")
    
    # Add prices to variations
    user_currency = getattr(current_user, 'currency', 'USD')
    conversion_rate = CONVERSION_RATES.get(user_currency, 1.0)
    
    for variation in item.get('variations', []):
        sku_id = str(variation.get('skuId'))
        if sku_id in prices:
            variation['price'] = round(prices[sku_id] * conversion_rate, 2)
            variation['currency'] = user_currency
    
    return jsonify([item])

@catalog_bp.route('/api/catalog/search')
@login_required
@cache.memoize(timeout=300)
def search_catalog():
    search_query = request.args.get('query', '').strip()
    if not search_query:
        return jsonify([])

    # Get filter parameters from query string
    product_type = request.args.get('type', 'singles')
    sort_by = request.args.get('sort', 'name')
    rarity = request.args.get('rarity', '')
    print_type = request.args.get('printType', '')
    condition = request.args.get('condition', '')
    language = request.args.get('language', '')
    
    # Set filter based on type
    is_single = True if product_type == 'singles' else False
    
    # Get game and expansion from query string
    game = request.args.get('game', '').strip()
    expansion = request.args.get('expansion', '').strip()
    
    # Build base query with case-insensitive name search
    query = {
        'name': {'$regex': search_query, '$options': 'i'},
        'isSingle': is_single,
        'isSealed': (not is_single)
    }

    # Add rarity filter if specified
    if rarity:
        query['extendedData__match'] = {
            'name': 'Rarity',
            'value': rarity
        }
    
    # Add game and expansion filters if provided
    if game:
        query['gameName'] = game
    if expansion:
        query['expansionName'] = expansion

    # Build aggregation pipeline
    pipeline = [
        # Match stage for initial filtering
        {'$match': query},
        
        # Unwind skus array to filter individual SKUs
        {'$unwind': {'path': '$skus', 'preserveNullAndEmptyArrays': False}},
        
        # Filter SKUs based on criteria
        {'$match': {
            'skus.printingName': {'$exists': True, '$ne': None},
            'skus.condAbbr': {'$exists': True, '$ne': None},
            'skus.langAbbr': {'$exists': True, '$ne': None}
        }},
        
        # Add additional SKU filters if specified
        *([{'$match': {'skus.printingName': print_type}}] if print_type else []),
        *([{'$match': {'skus.condAbbr': condition}}] if condition else []),
        *([{'$match': {'skus.langAbbr': language}}] if language else []),
        
        # Lookup inventory quantities
        {'$lookup': {
            'from': 'inventory',
            'let': {'skuId': '$skus.skuId'},
            'pipeline': [
                {'$match': {
                    '$expr': {
                        '$and': [
                            {'$eq': ['$userId', str(current_user.id)]},
                            {'$eq': ['$skuId', {'$toString': '$$skuId'}]}
                        ]
                    }
                }},
                {'$project': {'quantity': 1, '_id': 0}}
            ],
            'as': 'inventory'
        }},
        
        # Group back to reconstruct the document
        {'$group': {
            '_id': '$_id',
            'name': {'$first': '$name'},
            'number': {'$first': '$number'},
            'productId': {'$first': '$productId'},
            'imageUrl': {'$first': '$imageUrl'},
            'expansionName': {'$first': '$expansionName'},
            'extendedData': {'$first': '$extendedData'},
            'filteredSkus': {'$push': '$skus'},
            'inventory': {'$first': '$inventory'}
        }},
        
        # Lookup prices from local database with print type
        {'$lookup': {
            'from': 'prices',
            'let': {
                'productId': '$productId',
                'printType': '$skus.printingName'
            },
            'pipeline': [
                {'$match': {
                    '$expr': {
                        '$and': [
                            {'$eq': ['$productId', {'$toInt': '$$productId'}]},
                            {'$eq': ['$subTypeName', '$$printType']}
                        ]
                    }
                }},
                {'$project': {'priceData': 1}}
            ],
            'as': 'priceData'
        }},
        
        # Add price field for sorting
        {'$addFields': {
            'sortPrice': {
                '$ifNull': [
                    {'$toDouble': {'$arrayElemAt': ['$priceData.priceData.lowPrice', 0]}},
                    9999999  # Default high price for sorting
                ]
            }
        }},
        
        # Add sorting
        {'$sort': (
            {'name': 1} if sort_by == 'name' else
            {'number': 1} if sort_by == 'number' else
            {'sortPrice': 1} if sort_by == 'price_asc' else
            {'sortPrice': -1} if sort_by == 'price_desc' else
            {'_id': 1}
        )},
        
        # Project final structure
        {'$project': {
            'id': {'$toString': '$_id'},
            'name': 1,
            'number': 1,
            'productId': {'$toString': '$productId'},
            'imageUrl': 1,
            'expansionName': {'$ifNull': ['$expansionName', 'N/A']},
            'rarity': {
                '$let': {
                    'vars': {
                        'rarityData': {
                            '$filter': {
                                'input': '$extendedData',
                                'as': 'data',
                                'cond': {'$eq': ['$$data.name', 'Rarity']}
                            }
                        }
                    },
                    'in': {
                        '$ifNull': [
                            {'$arrayElemAt': ['$$rarityData.value', 0]},
                            ''
                        ]
                    }
                }
            },
            'skus': '$skus',
            'variations': {
                '$map': {
                    'input': '$skus',
                    'as': 'sku',
                    'in': {
                        'printType': '$$sku.printingName',
                        'condition': '$$sku.condAbbr',
                        'language': '$$sku.langAbbr',
                        'skuId': '$$sku.skuId',
                        'currentQuantity': {
                            '$let': {
                                'vars': {
                                    'inventoryMatch': {
                                        '$filter': {
                                            'input': '$inventory',
                                            'as': 'inv',
                                            'cond': {'$eq': ['$$inv.skuId', {'$toString': '$$sku.skuId'}]}
                                        }
                                    }
                                },
                                'in': {
                                    '$ifNull': [
                                        {'$arrayElemAt': ['$$inventoryMatch.quantity', 0]},
                                        0
                                    ]
                                }
                            }
                        }
                    }
                }
            },
            'skuMapping': {
                '$arrayToObject': {
                    '$map': {
                        'input': '$skus',
                        'as': 'sku',
                        'in': {
                            'k': {
                                '$concat': [
                                    '$$sku.printingName', '_',
                                    '$$sku.condAbbr', '_',
                                    '$$sku.langAbbr'
                                ]
                            },
                            'v': '$$sku.skuId'
                        }
                    }
                }
            }
        }}
    ]
    
    # Execute aggregation pipeline
    catalog_items = list(mongo.db.catalog.aggregate(pipeline))
    
    # Get all skuIds for price lookup
    sku_ids = []
    for item in catalog_items:
        for sku in item.get('skus', []):
            sku_ids.append(str(sku.get('skuId')))
    
    # Get prices from TCGPlayer API
    prices = {}
    if sku_ids:
        tcgplayer_token = get_tcgplayer_token()
        if tcgplayer_token:
            try:
                headers = {
                    'Authorization': f'Bearer {tcgplayer_token}',
                    'Accept': 'application/json'
                }
                # Split SKU IDs into smaller batches for more parallelization
                batch_size = 50  # Smaller batch size for more concurrent requests
                batches = [sku_ids[i:i + batch_size] for i in range(0, len(sku_ids), batch_size)]
                
                # Create session and tasks for concurrent requests
                async def fetch_all_prices():
                    connector = aiohttp.TCPConnector(limit=0)  # No limit on concurrent connections
                    timeout = aiohttp.ClientTimeout(total=30)  # 30 second timeout
                    
                    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                        async def get_batch_prices(batch):
                            url = f'https://api.tcgplayer.com/pricing/sku/{",".join(batch)}'
                            try:
                                async with session.get(url, headers=headers) as response:
                                    if response.status == 200:
                                        data = await response.json()
                                        if 'results' in data:
                                            for result in data['results']:
                                                sku_id = str(result.get('skuId'))
                                                low_price = result.get('lowPrice')
                                                if low_price:
                                                    prices[sku_id] = float(low_price)
                            except Exception as e:
                                print(f"Error fetching batch: {str(e)}")
                            return None

                        # Create and run all tasks concurrently
                        await asyncio.gather(*(get_batch_prices(batch) for batch in batches))
                
                # Run the async code in a new event loop
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(fetch_all_prices())
                finally:
                    loop.close()
            except Exception as e:
                print(f"Error getting TCGPlayer prices: {str(e)}")
    
    # If TCGPlayer prices not available, get from local database
    if not prices:
        try:
            # Get all prices from local database
            price_docs = list(mongo.db.prices.find({
                'productId': {'$in': [int(item['productId']) for item in catalog_items]}
            }))
            
            # Create mapping of productId and print type to price
            product_prices = {}
            for doc in price_docs:
                if doc.get('priceData', {}).get('lowPrice') and float(doc['priceData']['lowPrice']) > 0:
                    key = f"{doc['productId']}_{doc['subTypeName']}"
                    product_prices[key] = float(doc['priceData']['lowPrice'])
            
            # Map prices to skuIds
            for item in catalog_items:
                for sku in item.get('skus', []):
                    sku_id = str(sku.get('skuId'))
                    if sku_id not in prices:  # Don't override TCGPlayer prices
                        key = f"{item['productId']}_{sku.get('printingName')}"
                        if key in product_prices:
                            prices[sku_id] = product_prices[key]
        except Exception as e:
            print(f"Error getting local prices: {str(e)}")
    
    # Add prices to catalog items
    user_currency = getattr(current_user, 'currency', 'USD')
    conversion_rate = CONVERSION_RATES.get(user_currency, 1.0)
    
    for item in catalog_items:
        for variation in item.get('variations', []):
            sku_id = str(variation.get('skuId'))
            if sku_id in prices:
                variation['price'] = round(prices[sku_id] * conversion_rate, 2)
                variation['currency'] = user_currency
    
    return jsonify(catalog_items)

def get_tcgplayer_token():
    try:
        # Get the latest TCGPlayer key from the collection
        key_doc = mongo.db.tcgplayerKey.find_one({}, sort=[('_id', -1)])
        if key_doc and 'latestKey' in key_doc:
            return key_doc['latestKey']
    except Exception as e:
        print(f"Error getting TCGPlayer token: {str(e)}")
    return None

@catalog_bp.route('/api/catalog/<game>/<expansion>')
@login_required
@cache.memoize(timeout=300)
def get_catalog_items(game, expansion):
    # Get filter parameters from query string
    product_type = request.args.get('type', 'singles')
    sort_by = request.args.get('sort', 'name')
    rarity = request.args.get('rarity', '')
    print_type = request.args.get('printType', '')
    condition = request.args.get('condition', '')
    language = request.args.get('language', '')

    # Set filter based on type
    is_single = True if product_type == 'singles' else False
    
    # Build base query
    query = {
        'gameName': game,
        'expansionName': expansion,
        'isSingle': is_single,
        'isSealed': (not is_single)
    }
    
    # Add rarity filter if specified
    if rarity:
        query['extendedData__match'] = {
            'name': 'Rarity',
            'value': rarity
        }

    # Optimized aggregation pipeline
    pipeline = [
        # Initial match with all criteria
        {'$match': {
            **query,
            'skus': {
                '$elemMatch': {
                    'printingName': {'$exists': True, '$ne': None},
                    'condAbbr': {'$exists': True, '$ne': None},
                    'langAbbr': {'$exists': True, '$ne': None},
                    **({"printingName": print_type} if print_type else {}),
                    **({"condAbbr": condition} if condition else {}),
                    **({"langAbbr": language} if language else {})
                }
            }
        }},
        
        # Project only needed fields
        {'$project': {
            'name': 1,
            'number': 1,
            'productId': 1,
            'imageUrl': 1,
            'expansionName': 1,
            'extendedData': 1,
            'skus': {
                '$filter': {
                    'input': '$skus',
                    'as': 'sku',
                    'cond': {
                        '$and': [
                            {'$ne': ['$$sku.printingName', None]},
                            {'$ne': ['$$sku.condAbbr', None]},
                            {'$ne': ['$$sku.langAbbr', None]},
                            *([{'$eq': ['$$sku.printingName', print_type]}] if print_type else []),
                            *([{'$eq': ['$$sku.condAbbr', condition]}] if condition else []),
                            *([{'$eq': ['$$sku.langAbbr', language]}] if language else [])
                        ]
                    }
                }
            }
        }},
        
        # Lookup inventory quantities
        {'$lookup': {
            'from': 'inventory',
            'let': {'skuIds': '$skus.skuId'},
            'pipeline': [
                {'$match': {
                    '$expr': {
                        '$and': [
                            {'$eq': ['$userId', str(current_user.id)]},
                            {'$in': [{'$toInt': '$skuId'}, '$$skuIds']}
                        ]
                    }
                }},
                {'$project': {'skuId': 1, 'quantity': 1, '_id': 0}}
            ],
            'as': 'inventory'
        }},
        
        # Lookup prices from local database
        {'$lookup': {
            'from': 'prices',
            'let': {'productId': '$productId'},
            'pipeline': [
                {'$match': {
                    '$expr': {'$eq': ['$productId', {'$toInt': '$$productId'}]}
                }},
                {'$project': {'priceData': 1, 'subTypeName': 1}}
            ],
            'as': 'priceData'
        }},
        
        # Add price field for sorting
        {'$addFields': {
            'sortPrice': {
                '$let': {
                    'vars': {
                        'firstPrice': {
                            '$first': {
                                '$filter': {
                                    'input': '$priceData',
                                    'as': 'price',
                                    'cond': {
                                        '$and': [
                                            {'$gt': ['$$price.priceData.lowPrice', 0]},
                                            {'$eq': ['$$price.subTypeName', {'$first': '$skus.printingName'}]}
                                        ]
                                    }
                                }
                            }
                        }
                    },
                    'in': {
                        '$ifNull': [
                            {'$toDouble': '$$firstPrice.priceData.lowPrice'},
                            9999999
                        ]
                    }
                }
            }
        }},
        
        # Add sorting with index hint
        {'$sort': (
            {'name': 1} if sort_by == 'name' else
            {'number': 1} if sort_by == 'number' else
            {'sortPrice': 1} if sort_by == 'price_asc' else
            {'sortPrice': -1} if sort_by == 'price_desc' else
            {'_id': 1}
        )},
        
        # Project final structure
        {'$project': {
            'id': {'$toString': '$_id'},
            'name': 1,
            'number': 1,
            'productId': {'$toString': '$productId'},
            'imageUrl': 1,
            'expansionName': {'$ifNull': ['$expansionName', 'N/A']},
            'rarity': {
                '$let': {
                    'vars': {
                        'rarityData': {
                            '$filter': {
                                'input': '$extendedData',
                                'as': 'data',
                                'cond': {'$eq': ['$$data.name', 'Rarity']}
                            }
                        }
                    },
                    'in': {
                        '$ifNull': [
                            {'$arrayElemAt': ['$$rarityData.value', 0]},
                            ''
                        ]
                    }
                }
            },
            'skus': '$skus',
            'variations': {
                '$map': {
                    'input': '$skus',
                    'as': 'sku',
                    'in': {
                        'printType': '$$sku.printingName',
                        'condition': '$$sku.condAbbr',
                        'language': '$$sku.langAbbr',
                        'skuId': '$$sku.skuId',
                        'currentQuantity': {
                            '$ifNull': [
                                {'$arrayElemAt': ['$inventory.quantity', 0]},
                                0
                            ]
                        }
                    }
                }
            },
            'skuMapping': {
                '$arrayToObject': {
                    '$map': {
                        'input': '$skus',
                        'as': 'sku',
                        'in': {
                            'k': {
                                '$concat': [
                                    '$$sku.printingName', '_',
                                    '$$sku.condAbbr', '_',
                                    '$$sku.langAbbr'
                                ]
                            },
                            'v': '$$sku.skuId'
                        }
                    }
                }
            }
        }}
    ]
    
    # Execute aggregation pipeline
    catalog_items = list(mongo.db.catalog.aggregate(pipeline))
    
    # Get all skuIds for price lookup
    sku_ids = []
    for item in catalog_items:
        for sku in item.get('skus', []):
            sku_ids.append(str(sku.get('skuId')))
    
    # Get prices from TCGPlayer API
    prices = {}
    if sku_ids:
        tcgplayer_token = get_tcgplayer_token()
        if tcgplayer_token:
            try:
                headers = {
                    'Authorization': f'Bearer {tcgplayer_token}',
                    'Accept': 'application/json'
                }
                # Split SKU IDs into smaller batches for more parallelization
                batch_size = 50  # Smaller batch size for more concurrent requests
                batches = [sku_ids[i:i + batch_size] for i in range(0, len(sku_ids), batch_size)]
                
                # Create session and tasks for concurrent requests
                async def fetch_all_prices():
                    connector = aiohttp.TCPConnector(limit=0)  # No limit on concurrent connections
                    timeout = aiohttp.ClientTimeout(total=30)  # 30 second timeout
                    
                    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                        async def get_batch_prices(batch):
                            url = f'https://api.tcgplayer.com/pricing/sku/{",".join(batch)}'
                            try:
                                async with session.get(url, headers=headers) as response:
                                    if response.status == 200:
                                        data = await response.json()
                                        if 'results' in data:
                                            for result in data['results']:
                                                sku_id = str(result.get('skuId'))
                                                low_price = result.get('lowPrice')
                                                if low_price:
                                                    prices[sku_id] = float(low_price)
                            except Exception as e:
                                print(f"Error fetching batch: {str(e)}")
                            return None

                        # Create and run all tasks concurrently
                        await asyncio.gather(*(get_batch_prices(batch) for batch in batches))
                
                # Run the async code in a new event loop
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(fetch_all_prices())
                finally:
                    loop.close()
            except Exception as e:
                print(f"Error getting TCGPlayer prices: {str(e)}")
    
    # If TCGPlayer prices not available, get from local database
    if not prices:
        try:
            # Get all prices from local database
            price_docs = list(mongo.db.prices.find({
                'productId': {'$in': [int(item['productId']) for item in catalog_items]}
            }))
            
            # Create mapping of productId and print type to price
            product_prices = {}
            for doc in price_docs:
                if doc.get('priceData', {}).get('lowPrice') and float(doc['priceData']['lowPrice']) > 0:
                    key = f"{doc['productId']}_{doc['subTypeName']}"
                    product_prices[key] = float(doc['priceData']['lowPrice'])
            
            # Map prices to skuIds
            for item in catalog_items:
                for sku in item.get('skus', []):
                    sku_id = str(sku.get('skuId'))
                    if sku_id not in prices:  # Don't override TCGPlayer prices
                        key = f"{item['productId']}_{sku.get('printingName')}"
                        if key in product_prices:
                            prices[sku_id] = product_prices[key]
        except Exception as e:
            print(f"Error getting local prices: {str(e)}")
    
    # Add prices to catalog items
    user_currency = getattr(current_user, 'currency', 'USD')
    conversion_rate = CONVERSION_RATES.get(user_currency, 1.0)
    
    for item in catalog_items:
        for variation in item.get('variations', []):
            sku_id = str(variation.get('skuId'))
            if sku_id in prices:
                variation['price'] = round(prices[sku_id] * conversion_rate, 2)
                variation['currency'] = user_currency
    
    return jsonify(catalog_items)
