from flask import request, jsonify, current_app, url_for, render_template
from flask_login import current_user
from bson import ObjectId
from pymongo import UpdateOne
from datetime import datetime
import requests
import logging
from collections import defaultdict
import csv
import io
from itsdangerous import URLSafeTimedSerializer
from routes.buylist import buylist_bp
from routes.buylist.core import logger, order_collection, user_collection, catalog_collection, db
from routes.buylist.price_fetcher import get_updated_prices

def calculate_cost_of_goods(username):
    """
    Calculate the average cost of goods for each SKU purchased by the user.
    
    Args:
        username (str): The username to calculate cost of goods for
        
    Returns:
        dict: Dictionary mapping SKU IDs to cost of goods data
    """
    sku_purchases = defaultdict(lambda: {'total_value': 0, 'total_quantity': 0, 'purchase_count': 0})
    
    # Find all orders for the user
    user_orders = order_collection.find({'username': username})
    
    for order in user_orders:
        for item in order.get('line_items', []):
            sku_id = str(item.get('skuId'))  # Convert to string for consistency
            if sku_id:
                # Calculate the total value (treating credit as cash)
                item_value = item['offeredPrice'] * item['quantity']
                
                sku_purchases[sku_id]['total_value'] += item_value
                sku_purchases[sku_id]['total_quantity'] += item['quantity']
                sku_purchases[sku_id]['purchase_count'] += 1
    
    cost_of_goods = {}
    for sku_id, data in sku_purchases.items():
        if data['total_quantity'] > 0:
            cost_of_goods[sku_id] = {
                'average_cost': data['total_value'] / data['total_quantity'],
                'purchase_count': data['purchase_count']
            }
    
    return cost_of_goods

@buylist_bp.route('/api/update-order-status/<order_id>', methods=['POST'])
def update_order_status(order_id):
    """
    Update the status of an order.
    
    Args:
        order_id (str): The ID of the order to update
        
    Returns:
        JSON response with success or error message
    """
    try:
        data = request.json
        new_status = data.get('status')
        if not new_status:
            return jsonify({'success': False, 'error': 'New status is required'}), 400

        if new_status not in ['Pending', 'Accepted', 'Amendment', 'Complete', 'Staged']:
            return jsonify({'success': False, 'error': 'Invalid status'}), 400

        order = order_collection.find_one({'_id': ObjectId(order_id)})
        if not order:
            return jsonify({'success': False, 'error': 'Order not found'}), 404

        old_status = order.get('orderStatus', 'Unknown')

        if new_status == 'Accepted':
            from routes.buylist.email_service import send_acceptance_email
            result = send_acceptance_email(order)
            if not result['success']:
                return jsonify({'success': False, 'error': result['error']}), 500

        result = order_collection.update_one(
            {'_id': ObjectId(order_id)},
            {'$set': {'orderStatus': new_status}}
        )

        if result.modified_count > 0:
            # Send email notification for status change
            from routes.buylist.email_service import send_status_change_email
            email_result = send_status_change_email(order, old_status, new_status)
            if email_result['success']:
                return jsonify({'success': True, 'message': 'Order status updated and customer notified successfully'})
            else:
                return jsonify({'success': True, 'message': 'Order status updated but failed to notify customer', 'error': email_result['error']})
        else:
            return jsonify({'success': False, 'error': 'Status not changed'}), 400
    except Exception as e:
        logger.error(f"Error updating order status: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': 'An error occurred while updating the order status'}), 500

@buylist_bp.route('/api/update-sku', methods=['POST'])
def update_sku():
    """
    Update a SKU in an order.
    
    Returns:
        JSON response with success or error message
    """
    data = request.json
    order_id = data.get('orderId')
    item_index = data.get('itemIndex')
    condition = data.get('condition')
    print_type = data.get('printType')
    new_sku_id = data.get('newSkuId')
    new_offered_price = data.get('offeredPrice')
    customer_percentage = data.get('customerPercentage')

    try:
        order = order_collection.find_one({'_id': ObjectId(order_id)})
        if not order:
            return jsonify({'success': False, 'error': 'Order not found'}), 404

        item = order['line_items'][item_index]

        change_log_entry = {
            'timestamp': datetime.utcnow(),
            'item_index': item_index,
            'changes': {}
        }

        for field, new_value in [('condition', condition), ('printType', print_type), ('skuId', new_sku_id), ('offeredPrice', float(new_offered_price)), ('customerPercentage', customer_percentage)]:
            if new_value is not None and new_value != 'Not found':
                if field == 'skuId':
                    try:
                        new_value = int(new_value)
                    except ValueError:
                        return jsonify({'success': False, 'error': f'Invalid SKU ID: {new_value}'}), 400
                if item.get(field) != new_value:
                    change_log_entry['changes'][field] = {
                        'old': item.get(field),
                        'new': new_value
                    }
                    item[field] = new_value

        item['printType'] = print_type

        if 'offeredPrice' in change_log_entry['changes']:
            new_offered_price = round(float(new_offered_price), 2)
            item_type = item.get('type', '').lower()
            if item_type == 'cash':
                item['offeredCashPrice'] = new_offered_price
                change_log_entry['changes']['offeredCashPrice'] = {
                    'old': item.get('offeredCashPrice'),
                    'new': new_offered_price
                }
            elif item_type == 'credit':
                item['offeredCreditPrice'] = new_offered_price
                change_log_entry['changes']['offeredCreditPrice'] = {
                    'old': item.get('offeredCreditPrice'),
                    'new': new_offered_price
                }
            else:
                item['offeredCashPrice'] = new_offered_price
                item['offeredCreditPrice'] = round(new_offered_price * 1.25, 2)
                change_log_entry['changes']['offeredCashPrice'] = {
                    'old': item.get('offeredCashPrice'),
                    'new': item['offeredCashPrice']
                }
                change_log_entry['changes']['offeredCreditPrice'] = {
                    'old': item.get('offeredCreditPrice'),
                    'new': item['offeredCreditPrice']
                }

        result = order_collection.update_one(
            {'_id': ObjectId(order_id)},
            {
                '$set': {
                    f'line_items.{item_index}': item,
                    'orderStatus': 'Amendment'
                },
                '$push': {'change_log': change_log_entry}
            }
        )

        if result.modified_count > 0 or change_log_entry['changes']:
            sku_id_for_prices = new_sku_id if new_sku_id != 'Not found' else item['skuId']
            updated_prices = get_updated_prices(sku_id_for_prices)
            return jsonify({
                'success': True, 
                'message': 'Item updated successfully and order status changed to Amendment', 
                'newSkuId': new_sku_id if new_sku_id != 'Not found' else item['skuId'],
                'updatedPrices': updated_prices,
                'newPrintType': print_type,
                'newStatus': 'Amendment'
            })
        else:
            return jsonify({'success': False, 'error': 'No changes were made'}), 400

    except Exception as e:
        return jsonify({'success': False, 'error': 'An error occurred while updating the item'}), 500

@buylist_bp.route('/api/delete-order/<order_id>', methods=['DELETE'])
def delete_order(order_id):
    """
    Delete an order.
    
    Args:
        order_id (str): The ID of the order to delete
        
    Returns:
        JSON response with success or error message
    """
    try:
        result = order_collection.delete_one({'_id': ObjectId(order_id)})
        if result.deleted_count > 0:
            logger.info(f"Order {order_id} deleted successfully")
            return jsonify({'success': True, 'message': 'Order deleted successfully'})
        else:
            logger.warning(f"Order {order_id} not found for deletion")
            return jsonify({'success': False, 'error': 'Order not found'}), 404
    except Exception as e:
        logger.error(f"Error deleting order {order_id}: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': 'An error occurred while deleting the order'}), 500

@buylist_bp.route('/api/place-order', methods=['POST'])
def place_order():
    """
    Place a new order.
    
    Returns:
        JSON response with success or error message
    """
    try:
        data = request.json
        logger.info(f"Received order data: {data}")

        if not data or 'lineItems' not in data or not data['lineItems']:
            return jsonify({'success': False, 'error': 'Invalid order data'}), 400

        cash_total = 0
        credit_total = 0
        total = 0

        for item in data['lineItems']:
            item_total = item['quantity'] * item['offeredPrice']
            if item['type'] == 'cash':
                cash_total += item_total
            elif item['type'] == 'credit':
                credit_total += item_total
            total += item_total

        new_order = {
            'username': current_user.username,
            'date': datetime.utcnow(),
            'customer_name': data.get('customerName', ''),
            'customer_email': data.get('customerEmail', ''),  # Add customer email
            'staff_name': data.get('staffName', ''),
            'line_items': data['lineItems'],
            'cash_total': cash_total,
            'credit_total': credit_total,
            'total': total,
            'orderStatus': 'Pending',
            'isComplete': False
        }

        result = order_collection.insert_one(new_order)

        if result.inserted_id:
            logger.info(f"Order placed successfully. Order ID: {result.inserted_id}")
            return jsonify({'success': True, 'message': 'Order placed successfully', 'orderId': str(result.inserted_id)})
        else:
            logger.error("Failed to insert order into database")
            return jsonify({'success': False, 'error': 'Failed to place order'}), 500

    except Exception as e:
        logger.error(f"Error placing order: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': 'An error occurred while placing the order'}), 500

@buylist_bp.route('/api/update_prices')
def update_prices():
    """
    Update prices for a product.
    
    Returns:
        JSON response with updated prices
    """
    from routes.buylist.price_fetcher import fetch_tcgplayer_prices_batch
    
    product_id = request.args.get('productId')
    if not product_id:
        return jsonify({'success': False, 'error': 'Product ID is required'}), 400

    try:
        product = catalog_collection.find_one({'productId': int(product_id)})
        if not product:
            return jsonify({'success': False, 'error': 'Product not found'}), 404

        updated_prices = {}
        buylist_prices = {}
        
        # Get all SKUs for this product
        skus = product.get('skus', [])
        sku_ids = [sku['skuId'] for sku in skus]
        
        # Fetch prices for all SKUs in parallel
        logger.info(f"Fetching prices for {len(sku_ids)} SKUs in parallel for product {product_id}")
        price_data = fetch_tcgplayer_prices_batch(sku_ids)
        logger.info(f"Successfully fetched prices for {len(price_data)} SKUs")
        
        # Update each SKU with the fetched price data
        for sku in skus:
            sku_id = sku['skuId']
            sku_price_data = price_data.get(str(sku_id))
            
            if sku_price_data:
                low_price = sku_price_data.get('lowPrice', 0)
                
                updated_prices[sku_id] = low_price
                buylist_prices[sku_id] = {
                    'cash': round(low_price * 0.7, 2),
                    'credit': round(low_price * 0.8, 2)
                }
                
                # Update the sku in the product with the new price data
                sku['lowPrice'] = low_price
                sku['marketPrice'] = sku_price_data.get('marketPrice', low_price)
                sku['midPrice'] = sku_price_data.get('midPrice', 0)
                sku['highPrice'] = sku_price_data.get('highPrice', 0)
                
                logger.info(f"Updated price for SKU {sku_id}: lowPrice=${low_price}, marketPrice=${sku_price_data.get('marketPrice', 0)}")
            else:
                # Fallback to catalog price if API call fails
                low_price = sku.get('lowPrice', 0)
                updated_prices[sku_id] = low_price
                buylist_prices[sku_id] = {
                    'cash': round(low_price * 0.7, 2),
                    'credit': round(low_price * 0.8, 2)
                }
                logger.info(f"Using catalog price for SKU {sku_id}: ${low_price}")

        logger.info(f"Updated prices for product {product_id}: {updated_prices}")
        logger.info(f"Buylist prices for product {product_id}: {buylist_prices}")

        return jsonify({
            'success': True,
            'updated_prices': updated_prices,
            'buylist_prices': buylist_prices,
            'skus': skus
        })
    except Exception as e:
        logger.error(f"Error updating prices: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': 'An error occurred while updating prices'}), 500

@buylist_bp.route('/api/delete-item/<order_id>/<int:item_index>', methods=['DELETE'])
def delete_item(order_id, item_index):
    """
    Delete an item from an order.
    
    Args:
        order_id (str): The ID of the order
        item_index (int): The index of the item to delete
        
    Returns:
        JSON response with success or error message
    """
    try:
        # Find the order
        order = order_collection.find_one({'_id': ObjectId(order_id)})
        if not order:
            logger.error(f"Order not found: {order_id}")
            return jsonify({'success': False, 'error': 'Order not found'}), 404

        # Check if item_index is valid
        line_items = order.get('line_items', [])
        if item_index < 0 or item_index >= len(line_items):
            logger.error(f"Invalid item index: {item_index}")
            return jsonify({'success': False, 'error': 'Invalid item index'}), 400

        # Get the item to be deleted
        deleted_item = line_items[item_index]

        # Calculate the amount to subtract from totals
        item_total = deleted_item['quantity'] * deleted_item['offeredPrice']
        
        # Remove the item from line_items array
        line_items.pop(item_index)
        
        # Update totals
        updates = {
            '$set': {
                'line_items': line_items,
                'total': order.get('total', 0) - item_total
            }
        }
        
        if deleted_item.get('type') == 'cash':
            updates['$set']['cash_total'] = order.get('cash_total', 0) - item_total
        elif deleted_item.get('type') == 'credit':
            updates['$set']['credit_total'] = order.get('credit_total', 0) - item_total

        # Update the order
        result = order_collection.update_one(
            {'_id': ObjectId(order_id)},
            updates
        )

        if result.modified_count > 0:
            logger.info(f"Successfully deleted item {item_index} from order {order_id}")
            return jsonify({
                'success': True, 
                'message': 'Item deleted successfully',
                'deletedItem': deleted_item
            })
        else:
            logger.error(f"Failed to delete item {item_index} from order {order_id}")
            return jsonify({'success': False, 'error': 'Failed to delete item'}), 500

    except Exception as e:
        logger.error(f"Error deleting item from order: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'An error occurred: {str(e)}'}), 500

@buylist_bp.route('/api/create-csv/<order_id>', methods=['GET'])
def create_csv(order_id):
    """
    Create a CSV file for an order.
    
    Args:
        order_id (str): The ID of the order
        
    Returns:
        JSON response with CSV data
    """
    try:
        order = order_collection.find_one({'_id': ObjectId(order_id)})
        if not order:
            return jsonify({'success': False, 'error': 'Order not found'}), 404

        csv_data = io.StringIO()
        csv_writer = csv.writer(csv_data)

        for item in order['line_items']:
            product_id = item.get('productId')
            sku_id = item.get('skuId')
            quantity = item.get('quantity', 1)

            if product_id and sku_id:
                catalog_item = catalog_collection.find_one({'productId': product_id})
                category_id = catalog_item.get('categoryId', '') if catalog_item else ''
                csv_writer.writerow([category_id, sku_id, quantity])

        csv_output = csv_data.getvalue()
        csv_data.close()

        return jsonify({
            'success': True,
            'csv_data': csv_output,
            'filename': f'order_{order_id}_items.csv'
        })

    except Exception as e:
        logger.error(f"Error creating CSV for order {order_id}: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': 'An error occurred while creating the CSV'}), 500
