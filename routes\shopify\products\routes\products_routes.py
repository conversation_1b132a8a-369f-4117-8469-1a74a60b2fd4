import logging
from routes.shopify.products.routes import products_bp
from routes.shopify.products.routes.products import (
    products,
    get_vendors,
    get_product_types,
    get_expansion_names,
    get_products,
    get_product,
    delete_product,
    update_manual_override
)

logger = logging.getLogger(__name__)
logger.info("Registering products routes")

# Register routes
products_bp.route('/')(products)
products_bp.route('/api/vendors')(get_vendors)
products_bp.route('/api/product-types')(get_product_types)
products_bp.route('/api/expansion-names')(get_expansion_names)
products_bp.route('/api/products')(get_products)
products_bp.route('/api/product/<product_id>')(get_product)
products_bp.route('/api/product/delete', methods=['POST'])(delete_product)
products_bp.route('/api/product/manual-override', methods=['POST'])(update_manual_override)

logger.info("Products routes registered successfully")
