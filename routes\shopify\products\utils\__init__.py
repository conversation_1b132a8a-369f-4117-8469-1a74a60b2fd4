from flask import Blueprint
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprints for each utility module
pricing_bp = Blueprint('pricing', __name__)
shopify_bp = Blueprint('shopify', __name__)
currency_bp = Blueprint('currency', __name__)
staged_bp = Blueprint('staged', __name__)

def get_shopify_headers(access_token):
    """
    Get the headers required for Shopify API requests

    Args:
        access_token (str): The Shopify access token

    Returns:
        dict: The headers for Shopify API requests
    """
    return {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': access_token
    }

def get_shopify_store_url(store_name):
    """
    Get the Shopify store URL

    Args:
        store_name (str): The Shopify store name

    Returns:
        str: The Shopify store URL
    """
    return f"https://{store_name}.myshopify.com/admin/api/2023-04"
