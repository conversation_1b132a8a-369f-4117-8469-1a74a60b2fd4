from mongoengine import Document, <PERSON><PERSON>ield, DateTimeField, ReferenceField, <PERSON><PERSON>anField
from datetime import datetime
from models.event_model import Event

class Attendee(Document):
    # Basic Info
    full_name = StringField(required=True)
    email = StringField(required=True)
    
    # Event reference
    event = ReferenceField(Event, required=True)
    
    # Registration details
    registration_date = DateTimeField(default=datetime.utcnow)
    registration_source = StringField(default="manual")  # manual, shopify, public_website, etc.
    
    # Status
    checked_in = BooleanField(default=False)
    checked_in_time = DateTimeField()
    
    # Marketing preferences
    future_events_opt_in = BooleanField(default=True)
    
    # Additional info
    notes = StringField()
    
    meta = {
        'collection': 'attendees',
        'indexes': [
            'email',
            'event',
            'registration_date',
            'checked_in'
        ]
    }
    
    def check_in(self):
        """Mark attendee as checked in"""
        self.checked_in = True
        self.checked_in_time = datetime.utcnow()
        self.save()
