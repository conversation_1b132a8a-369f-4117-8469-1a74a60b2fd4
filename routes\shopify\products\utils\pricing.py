import logging
from typing import Dict, List, Optional, Tuple, Union
import requests
from datetime import datetime
from .currency import convert_price_to_currency

logger = logging.getLogger(__name__)

def determine_printing_type(variant_title: str, valid_subtypes: List[str]) -> str:
    """
    Determine the printing type from variant title and valid subtypes
    """
    def try_match(title: str, subtypes_lower: set) -> Optional[str]:
        if 'foil' in title:
            if 'cold foil' in title and 'cold foil' in subtypes_lower:
                return 'Cold Foil'
            if 'rainbow foil' in title and 'rainbow foil' in subtypes_lower:
                return 'Rainbow Foil'
            if 'reverse holofoil' in title and any('reverse holofoil' in s for s in subtypes_lower):
                return 'Reverse Holofoil'
            if 'holofoil' in subtypes_lower:
                return 'Holofoil'
            if any('foil' in s for s in subtypes_lower):
                for subtype in valid_subtypes:
                    if 'foil' in subtype.lower():
                        return subtype
        return None

    if not valid_subtypes:
        return 'Normal'

    if len(valid_subtypes) == 1:
        return valid_subtypes[0]

    variant_title = variant_title.lower()
    subtypes_lower = {s.lower() for s in valid_subtypes}
    
    match = try_match(variant_title, subtypes_lower)
    if match:
        return match
        
    cleaned_title = variant_title.replace('extended art', '').strip()
    match = try_match(cleaned_title, subtypes_lower)
    if match:
        return match
        
    final_title = cleaned_title.replace('1st edition', '').strip()
    match = try_match(final_title, subtypes_lower)
    if match:
        return match
    
    return 'Normal'

def get_tcgplayer_prices(product_id: str, tcgplayer_api_key: str) -> Optional[List[Dict]]:
    """
    Get TCGPlayer pricing data for a product
    """
    try:
        headers = {
            'Authorization': f'Bearer {tcgplayer_api_key}',
            'Accept': 'application/json',
        }
        
        pricing_url = f"https://api.tcgplayer.com/pricing/product/{product_id}"
        pricing_response = requests.get(pricing_url, headers=headers, timeout=10)
        pricing_response.raise_for_status()
        
        return pricing_response.json().get('results', [])
        
    except Exception as e:
        logger.error(f"Error fetching TCGPlayer prices for product {product_id}: {str(e)}")
        return None

def get_price_from_collection(product_id: str, subtype_name: str, db) -> Optional[Dict]:
    """
    Get price from collection based on preference order
    """
    collection_price = db.prices.find_one({
        'productId': product_id,
        'subTypeName': subtype_name
    })
    
    if collection_price:
        # Convert collection price to same format as TCGPlayer pricing data
        return {
            'lowPrice': collection_price.get('lowPrice'),
            'marketPrice': collection_price.get('marketPrice'),
            'midPrice': collection_price.get('midPrice'),
            'highPrice': collection_price.get('highPrice'),
            'subTypeName': subtype_name
        }
    return None

def get_pricing_rules(user_profile: Dict, product: Dict) -> Dict:
    """
    Get pricing rules for a specific product
    """
    advanced_key = f"{product.get('vendor')}_{product.get('product_type')}_{product.get('expansionName')}"
    
    if advanced_key in user_profile.get('advancedPricingRules', {}):
        return user_profile['advancedPricingRules'][advanced_key]
    return user_profile.get('customStepping', {'nm': 100, 'lp': 90, 'mp': 85, 'hp': 75, 'dm': 65})

def apply_game_rules(price: float, game_name: str, product_type: str, rarity: str,
                    variant_title: str, game_settings: Dict) -> float:
    """
    Apply game-specific pricing rules
    """
    if not product_type or not game_settings:
        return price
            
    # Get print type from title
    variant_lower = variant_title.lower() if variant_title else ''
    print_type = "Normal"  # Default to Normal if no specific print type found
    if 'print_types' in game_settings:
        # Check for specific print types first
        for pt in game_settings['print_types'].keys():
            if pt != "Normal" and pt.lower() in variant_lower:
                print_type = pt
                break
                
    # Apply print type minimum
    print_type_min = game_settings.get('print_types', {}).get(print_type)
    if print_type_min is not None:
        # Print type minimum takes precedence
        price = max(price, print_type_min)
        return price  # Return immediately if print type minimum is applied
        
    # Only check other minimums if no print type minimum was applied
    game_default_min = game_settings.get('default_min_price')
    if game_default_min is not None:
        price = max(price, game_default_min)
            
    if rarity and 'rarities' in game_settings:
        rarity_min = game_settings['rarities'].get(rarity)
        if rarity_min is not None:
            price = max(price, rarity_min)
                
    return price

def apply_condition_stepping(price: float, condition: str, vendor: str,
                           product_type: str, expansion: str, stepping_rules: Dict) -> float:
    """
    Apply condition-based price stepping
    
    Args:
        price (float): Base price to apply stepping to
        condition (str): Card condition (e.g., 'Near Mint', 'Lightly Played')
        vendor (str): Vendor name
        product_type (str): Product type
        expansion (str): Expansion name
        stepping_rules (Dict): Dictionary of condition stepping rules
        
    Returns:
        float: Price after applying condition stepping
    """
    logger = logging.getLogger(__name__)
    
    condition_map = {
        'near mint': 'nm', 'near-mint': 'nm', 'nearmint': 'nm', 'nm': 'nm',
        'lightly played': 'lp', 'lightly-played': 'lp', 'lightlyplayed': 'lp', 'lp': 'lp',
        'moderately played': 'mp', 'moderately-played': 'mp', 'moderatelyplayed': 'mp', 'mp': 'mp',
        'heavily played': 'hp', 'heavily-played': 'hp', 'heavilyplayed': 'hp', 'hp': 'hp',
        'damaged': 'dm', 'dm': 'dm'
    }
        
    condition_code = condition_map.get(condition.lower().strip(), 'nm')
    stepping_percentage = stepping_rules.get(condition_code, 100)
    
    # Convert percentage to decimal
    stepping_decimal = float(stepping_percentage) / 100
    
    logger.info(f"Condition: {condition} -> {condition_code}")
    logger.info(f"Stepping percentage: {stepping_percentage}% -> {stepping_decimal}")
    logger.info(f"Original price: ${price}")
    logger.info(f"After stepping: ${round(price * stepping_decimal, 2)}")
    
    return round(price * stepping_decimal, 2)

def apply_price_rounding(price: float, rounding_enabled: bool, thresholds: List[int]) -> float:
    """
    Apply price rounding based on thresholds
    """
    if not rounding_enabled:
        return round(price, 2)
        
    thresholds = sorted(thresholds)
    dollars = int(price)
    cents = round((price - dollars) * 100)
    
    for threshold in thresholds:
        if cents <= threshold:
            return dollars + (threshold / 100)
            
    return dollars + 1
