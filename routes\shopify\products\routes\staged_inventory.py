import logging
from flask import request, jsonify
from flask_login import login_required, current_user
from bson import ObjectId

from ..config import staged_inventory_collection, shopify_collection
from ..utils import (
    process_staged_inventory,
    update_staged_inventory_match,
    delete_staged_inventory,
    update_product_variants
)

logger = logging.getLogger(__name__)

@login_required
def get_staged_inventory():
    """Get staged inventory items with matched variants"""
    try:
        logger.info("Starting staged inventory fetch")
        query = {'username': current_user.username}
        
        staged_inventory = list(staged_inventory_collection.find(query))
        logger.info(f"Found {len(staged_inventory)} staged inventory items")
        
        processed_items = []
        for item in staged_inventory:
            try:
                if '_id' in item:
                    item['_id'] = str(item['_id'])
                
                # Process staged item to find matching variants
                result = process_staged_inventory(item)
                item['matchedVariants'] = result['matchedVariants']
                item['unmatchedVariants'] = result['unmatchedVariants']
                
                processed_items.append(item)
                logger.info(f"Processed staged item: {item.get('name')}")
                
            except Exception as e:
                logger.error(f"Error processing staged item: {str(e)}")
                continue
        
        return jsonify(processed_items)
        
    except Exception as e:
        logger.error(f"Error fetching staged inventory: {str(e)}")
        return jsonify({'error': str(e)}), 500

@login_required
def delete_staged_item(item_id):
    """Delete a staged inventory item"""
    try:
        result = delete_staged_inventory(ObjectId(item_id), current_user.username)
        
        if result:
            return jsonify({
                "message": "Item deleted successfully",
                "error": None
            }), 200
        else:
            return jsonify({
                "message": "Item not found or could not be deleted",
                "error": None
            }), 200
            
    except Exception as e:
        logger.error(f"Error deleting staged inventory item: {str(e)}")
        return jsonify({
            "message": "Failed to delete item",
            "error": str(e)
        }), 500

@login_required
def match_variant():
    """Match a staged variant with a Shopify variant"""
    data = request.json
    item_id = data.get('itemId')
    variant_id = data.get('variantId')
    
    if not item_id or not variant_id:
        return jsonify({
            'message': 'Missing required fields',
            'error': None
        }), 200
        
    try:
        result = update_staged_inventory_match(
            ObjectId(item_id),
            variant_id,
            current_user.username
        )
        
        if result:
            return jsonify({
                'message': 'Variant matched successfully',
                'error': None
            })
        else:
            return jsonify({
                'message': 'Failed to match variant',
                'error': None
            }), 200
            
    except Exception as e:
        logger.error(f"Error matching variant: {str(e)}")
        return jsonify({
            'message': 'Failed to match variant',
            'error': str(e)
        }), 500

@login_required
def save_all_matches():
    """Save multiple variant matches at once"""
    try:
        data = request.json
        matches = data.get('matches', [])
        
        if not matches:
            return jsonify({
                'message': 'No matches provided',
                'error': None
            }), 200
            
        success_count = 0
        error_count = 0
        
        for match in matches:
            try:
                item_id = match.get('itemId')
                variant = match.get('variant')
                
                if not item_id or not variant:
                    error_count += 1
                    continue
                    
                result = update_staged_inventory_match(
                    ObjectId(item_id),
                    variant['id'],
                    current_user.username
                )
                
                if result:
                    success_count += 1
                else:
                    error_count += 1
                    
            except Exception as e:
                logger.error(f"Error saving match: {str(e)}")
                error_count += 1
                
        return jsonify({
            'message': f'Successfully saved {success_count} matches with {error_count} errors',
            'success_count': success_count,
            'error_count': error_count,
            'error': None
        })
        
    except Exception as e:
        logger.error(f"Error in save_all_matches: {str(e)}")
        return jsonify({
            'message': 'Failed to save matches',
            'error': str(e)
        }), 500

@login_required
def sync_to_shopify():
    """Sync staged inventory to Shopify"""
    data = request.json
    product_id = data.get('productId')
    item_id = data.get('itemId')
    
    try:
        # Handle staged inventory item if provided
        if item_id:
            staged_item = staged_inventory_collection.find_one({
                '_id': ObjectId(item_id), 
                'username': current_user.username
            })
            if not staged_item or not staged_item.get('shProduct', {}).get('variant'):
                return jsonify({
                    'message': 'Invalid staged inventory item',
                    'error': None
                }), 200

            # Update product variant quantity
            product = shopify_collection.find_one({
                'username': current_user.username,
                'id': staged_item['shProduct']['id']
            })
            if not product:
                return jsonify({
                    'message': 'Product not found',
                    'error': None
                }), 200

            # Update only the matching variant quantity
            variant_info = staged_item['shProduct']['variant']
            updated_variants = []
            for variant in product['variants']:
                if str(variant['id']) == str(variant_info['id']):
                    variant['inventory_quantity'] = staged_item.get('quantity', 0)
                updated_variants.append(variant)

            # Update MongoDB and cleanup staged item
            shopify_collection.update_one(
                {'_id': product['_id']},
                {'$set': {'variants': updated_variants}}
            )
            staged_inventory_collection.delete_one({'_id': ObjectId(item_id)})
            product_id = str(product['_id'])

        # Get product if not already fetched
        if not product_id:
            return jsonify({
                'message': 'Product ID is required',
                'error': None
            }), 200

        if not 'product' in locals():
            product = shopify_collection.find_one({
                '_id': ObjectId(product_id), 
                'username': current_user.username
            })
            if not product:
                return jsonify({
                    'message': 'Product not found',
                    'error': None
                }), 200

        # Push to Shopify
        success = update_product_variants(
            username=current_user.username,
            product_id=product['id'],
            variants=product['variants'],
            inventory_updates=True
        )

        if success:
            return jsonify({
                'message': 'Successfully synced with Shopify',
                'variants_updated': len(product['variants']),
                'success': True
            })
        else:
            return jsonify({
                'message': 'Failed to sync with Shopify',
                'error': 'Shopify API update failed'
            }), 500

    except Exception as e:
        logger.error(f"Error during sync: {str(e)}")
        return jsonify({'error': str(e)}), 500
