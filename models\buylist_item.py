from typing import Dict, Any, Optional
from datetime import datetime
from bson import ObjectId

class BuylistItem:
    def __init__(self, data: Dict[str, Any]):
        self._id: ObjectId = ObjectId(data.get('_id', ObjectId()))
        self.product_id: int = data['product_id']
        self.buy_price: float = data['buy_price']
        self.condition: str = data['condition']
        self.language: str = data['language']
        self.printing_type: str = data['printing_type']
        self.quantity_desired: int = data.get('quantity_desired', 1)
        self.max_quantity: Optional[int] = data.get('max_quantity')
        self.date_added: datetime = data.get('date_added', datetime.utcnow())
        self.last_updated: datetime = data.get('last_updated', datetime.utcnow())
        self.is_active: bool = data.get('is_active', True)
        self.notes: Optional[str] = data.get('notes')
        self.user_id: Optional[str] = data.get('user_id')  # If you have user-specific buylists
        self.price_percentage: Optional[float] = data.get('price_percentage')  # Percentage of market price
        self.auto_update_price: bool = data.get('auto_update_price', False)
        self.min_price: Optional[float] = data.get('min_price')
        self.max_price: Optional[float] = data.get('max_price')

    def to_dict(self) -> Dict[str, Any]:
        return {
            '_id': str(self._id),
            'product_id': self.product_id,
            'buy_price': self.buy_price,
            'condition': self.condition,
            'language': self.language,
            'printing_type': self.printing_type,
            'quantity_desired': self.quantity_desired,
            'max_quantity': self.max_quantity,
            'date_added': self.date_added.isoformat(),
            'last_updated': self.last_updated.isoformat(),
            'is_active': self.is_active,
            'notes': self.notes,
            'user_id': self.user_id,
            'price_percentage': self.price_percentage,
            'auto_update_price': self.auto_update_price,
            'min_price': self.min_price,
            'max_price': self.max_price
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BuylistItem':
        return cls(data)

    def update(self, data: Dict[str, Any]) -> None:
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.last_updated = datetime.utcnow()

    def calculate_buy_price(self, market_price: float) -> float:
        if self.price_percentage is not None:
            calculated_price = market_price * (self.price_percentage / 100)
            if self.min_price is not None:
                calculated_price = max(calculated_price, self.min_price)
            if self.max_price is not None:
                calculated_price = min(calculated_price, self.max_price)
            return round(calculated_price, 2)
        return self.buy_price

    def is_within_price_range(self, price: float) -> bool:
        if self.min_price is not None and price < self.min_price:
            return False
        if self.max_price is not None and price > self.max_price:
            return False
        return True

    def __repr__(self) -> str:
        return f"<BuylistItem(product_id={self.product_id}, condition={self.condition}, buy_price={self.buy_price})>"
