from flask import render_template, current_app, url_for
from flask_login import current_user
from itsdangerous import URLSafeTimedSerializer
from datetime import datetime
import requests
import logging
from routes.buylist.core import logger, user_collection, order_collection
from routes.buylist import buylist_bp

def generate_acceptance_email(order):
    """
    Generate the HTML content for an acceptance email.
    
    Args:
        order (dict): The order data
        
    Returns:
        str: The HTML content for the email
    """
    user_settings = user_collection.find_one({'username': current_user.username})
    if not user_settings or 'address' not in user_settings:
        logger.error(f"User settings or address not found for user: {current_user.username}")
        return "Error: User address not found"

    customer_email = order.get('customer_email')
    if not customer_email:
        logger.error(f"Customer email not found for order: {order['_id']}")
        return "Error: Customer email not found"

    business_name = user_settings.get('business_name', 'Your Business Name')

    address = user_settings['address']
    formatted_address = f"{address['street_address']}, {address['city']}, {address['state']}, {address['zip_code']}, {address['country']}".title()

    return render_template('acceptance_email.html',
                           customer_name=order.get('customer_name', 'Valued Customer'),
                           order_id=order['_id'],
                           address=formatted_address,
                           business_name=business_name,
                           current_year=datetime.now().year,
                           acceptance_date=datetime.now().strftime('%B %d, %Y'),
                           total_items=len(order.get('line_items', [])),
                           order_total=f"{order.get('total', 0):.2f}")

def send_acceptance_email(order):
    """
    Send an acceptance email to the customer.
    
    Args:
        order (dict): The order data
        
    Returns:
        dict: Success status and message
    """
    email_content = generate_acceptance_email(order)
    customer_email = order.get('customer_email')

    if not customer_email:
        logger.error(f"Customer email not found for order {order['_id']}")
        return {'success': False, 'error': 'Customer email not found'}

    try:
        mailgun_domain = current_app.config.get('MAILGUN_DOMAIN')
        mailgun_api_key = current_app.config.get('MAILGUN_API_KEY')

        user_settings = user_collection.find_one({'username': current_user.username})
        if not user_settings or 'email' not in user_settings:
            logger.error(f"Sender email not found in user settings for user: {current_user.username}")
            return {'success': False, 'error': 'Sender email not found in user settings'}
        sender_email = user_settings['email']

        if not mailgun_domain or not mailgun_api_key:
            logger.error("Mailgun configuration is incomplete")
            return {'success': False, 'error': 'Email service configuration is incomplete'}

        response = requests.post(
            f"https://api.eu.mailgun.net/v3/{mailgun_domain}/messages",
            auth=("api", mailgun_api_key),
            data={
                "from": sender_email,
                "to": customer_email,
                "subject": "Your Order Has Been Accepted",
                "html": email_content
            }
        )

        if response.status_code == 200:
            logger.info(f"Acceptance email sent to {customer_email} for order {order['_id']}")
            return {'success': True, 'message': 'Acceptance email sent successfully'}
        else:
            logger.error(f"Failed to send acceptance email. Status code: {response.status_code}, Response: {response.text}")
            return {'success': False, 'error': f"Failed to send email. Status code: {response.status_code}"}
    except Exception as e:
        logger.error(f"Error sending acceptance email: {str(e)}", exc_info=True)
        return {'success': False, 'error': f"Error sending email: {str(e)}"}

def send_status_change_email(order, old_status, new_status):
    """
    Send an email notification for a status change.
    
    Args:
        order (dict): The order data
        old_status (str): The previous status
        new_status (str): The new status
        
    Returns:
        dict: Success status and message
    """
    try:
        customer_email = order.get('customer_email')
        if not customer_email:
            return {'success': False, 'error': 'Customer email not found'}

        user_settings = user_collection.find_one({'username': current_user.username})
        if not user_settings or 'email' not in user_settings:
            return {'success': False, 'error': 'Sender email not found in user settings'}

        sender_email = user_settings['email']
        business_name = user_settings.get('business_name', 'Your Business Name')

        # Generate a token for the order link
        s = URLSafeTimedSerializer(current_app.config['SECRET_KEY'])
        token = s.dumps(str(order['_id']))
        order_link = url_for('buylist.view_order', token=token, _external=True)

        subject = f"Your Order Status Has Been Updated - {order['_id']}"
        body = render_template('status_change_email.html',
                               customer_name=order.get('customer_name', 'Valued Customer'),
                               order_id=order['_id'],
                               old_status=old_status,
                               new_status=new_status,
                               business_name=business_name,
                               order_link=order_link)

        mailgun_domain = current_app.config.get('MAILGUN_DOMAIN')
        mailgun_api_key = current_app.config.get('MAILGUN_API_KEY')

        if not mailgun_domain or not mailgun_api_key:
            return {'success': False, 'error': 'Email service configuration is incomplete'}

        response = requests.post(
            f"https://api.eu.mailgun.net/v3/{mailgun_domain}/messages",
            auth=("api", mailgun_api_key),
            data={
                "from": sender_email,
                "to": customer_email,
                "subject": subject,
                "html": body
            }
        )

        if response.status_code == 200:
            logger.info(f"Status change email sent to {customer_email} for order {order['_id']}")
            return {'success': True, 'message': 'Status change email sent successfully'}
        else:
            logger.error(f"Failed to send status change email. Status code: {response.status_code}, Response: {response.text}")
            return {'success': False, 'error': f"Failed to send email. Status code: {response.status_code}"}
    except Exception as e:
        logger.error(f"Error sending status change email: {str(e)}", exc_info=True)
        return {'success': False, 'error': f"Error sending email: {str(e)}"}

@buylist_bp.route('/api/send-acceptance-email/<order_id>', methods=['POST'])
def send_acceptance_email_route(order_id):
    """
    Route to send an acceptance email for an order.
    
    Args:
        order_id (str): The ID of the order
        
    Returns:
        JSON response with success or error message
    """
    from flask import jsonify
    from flask_login import login_required
    from bson import ObjectId
    
    @login_required
    def handle_request():
        try:
            order = order_collection.find_one({'_id': ObjectId(order_id)})
            if not order:
                return jsonify({'success': False, 'error': 'Order not found'}), 404

            result = send_acceptance_email(order)
            if not result['success']:
                return jsonify(result), 500
        
            update_result = order_collection.update_one(
                {'_id': ObjectId(order_id)},
                {'$set': {'orderStatus': 'Accepted'}}
            )

            if update_result.modified_count > 0:
                return jsonify({'success': True, 'message': 'Acceptance email sent and order status updated'})
            else:
                return jsonify({'success': False, 'error': 'Failed to update order status'}), 500
        except Exception as e:
            logger.error(f"Error in send_acceptance_email_route: {str(e)}", exc_info=True)
            return jsonify({'success': False, 'error': 'An error occurred while processing the acceptance email'}), 500
    
    return handle_request()

@buylist_bp.route('/api/send-order-update-email/<order_id>', methods=['GET', 'POST'])
def send_order_update_email(order_id):
    """
    Send an email with order updates to the customer.
    
    Args:
        order_id (str): The ID of the order
        
    Returns:
        JSON response with success or error message
    """
    from flask import jsonify, request
    from flask_login import login_required
    from bson import ObjectId
    
    @login_required
    def handle_request():
        try:
            order = order_collection.find_one({'_id': ObjectId(order_id)})
            if not order:
                return jsonify({'success': False, 'error': 'Order not found'}), 404

            customer_email = order.get('customer_email')
            if not customer_email:
                return jsonify({'success': False, 'error': 'Customer email not found'}), 400

            # Fetch the original order
            original_order = order.get('original_order', {})

            # Get user settings for business details
            user_settings = user_collection.find_one({'username': current_user.username})
            if not user_settings:
                return jsonify({'success': False, 'error': 'User settings not found'}), 400

            business_name = user_settings.get('business_name', 'Your Business Name')
            address = user_settings.get('address', {})
            formatted_address = f"{address.get('street_address', '')}, {address.get('city', '')}, {address.get('state', '')}, {address.get('zip_code', '')}, {address.get('country', '')}".title()

            # Generate a token for the order link
            s = URLSafeTimedSerializer(current_app.config['SECRET_KEY'])
            token = s.dumps(str(order['_id']))
            order_link = url_for('buylist.view_order', token=token, _external=True)

            # Create email content
            subject = "Your Order Has Been Updated"
            body = render_template('order_update_email.html', 
                                original_order=original_order, 
                                updated_order=order,
                                business_name=business_name,
                                business_address=formatted_address,
                                current_year=datetime.now().year,
                                change_log=order.get('change_log', []),
                                order_link=order_link)

            # Log the order details for debugging
            current_app.logger.debug(f"Original order: {original_order}")
            current_app.logger.debug(f"Updated order: {order}")
            current_app.logger.debug(f"Change log: {order.get('change_log', [])}")

            # For preview, return the email content
            if request.args.get('preview'):
                return body

            # Get the sender email from user_settings
            sender_email = user_settings.get('email')
            if not sender_email:
                return jsonify({'success': False, 'error': 'Sender email not found in user settings'}), 400

            # Send email using Mailgun
            mailgun_domain = current_app.config['MAILGUN_DOMAIN']
            mailgun_api_key = current_app.config['MAILGUN_API_KEY']

            response = requests.post(
                f"https://api.eu.mailgun.net/v3/{mailgun_domain}/messages",
                auth=("api", mailgun_api_key),
                data={"from": sender_email,
                    "to": customer_email,
                    "subject": subject,
                    "html": body})

            if response.status_code == 200:
                return jsonify({'success': True, 'message': 'Order update email sent successfully'})
            else:
                current_app.logger.error(f"Mailgun API error: {response.text}")
                return jsonify({'success': False, 'error': 'Failed to send email'}), 500

        except Exception as e:
            current_app.logger.error(f"Error sending order update email: {str(e)}")
            return jsonify({'success': False, 'error': 'An error occurred while sending the email'}), 500
    
    return handle_request()
