from mongoengine import Document, StringField, IntField, FloatField, ListField, DictField, DateTimeField, BooleanField, EmbeddedDocument, EmbeddedDocumentField

class ExtendedData(EmbeddedDocument):
    name = StringField()
    displayName = StringField()
    value = StringField()

class Price(EmbeddedDocument):
    productId = IntField()
    lowPrice = FloatField()
    midPrice = FloatField()
    highPrice = FloatField()
    marketPrice = FloatField()
    directLowPrice = FloatField()
    subTypeName = StringField()
    lastUpdated = DateTimeField()

class PresaleInfo(EmbeddedDocument):
    isPresale = BooleanField()
    releasedOn = DateTimeField()
    note = StringField()

class PricingInfo(EmbeddedDocument):
    lastupdated = StringField()
    lowPrice = FloatField()
    lowestShipping = FloatField()
    lowestListingPrice = FloatField()
    marketPrice = FloatField()
    directLowPrice = FloatField()

class Sku(EmbeddedDocument):
    skuId = IntField()
    productId = IntField()
    languageId = IntField()
    printingId = IntField()
    conditionId = IntField()
    condAbbr = StringField()
    condName = StringField()
    conditionAbbreviation = StringField()
    printingCode = StringField()
    printingName = StringField()
    languageName = StringField()
    langAbbr = StringField()
    languageAbbreviation = StringField()
    binderSku = StringField()
    directLowPrice = FloatField()
    lowPrice = FloatField()
    lowestListingPrice = FloatField()
    lowestShipping = FloatField()
    marketPrice = FloatField()
    lastupdated = StringField()
    pricingInfo = EmbeddedDocumentField(PricingInfo)
    pricing = DictField()  # Add this field
    lastUpdated = StringField()  # Add this field

class Catalog(Document):
    abbreviation = StringField()
    blueprint_id = IntField()
    categoryId = IntField()
    cleanName = StringField()
    expansionAbbreviation = StringField()
    expansionName = StringField()
    extendedData = ListField(EmbeddedDocumentField(ExtendedData))
    gameAbbreviation = StringField()
    gameName = StringField()
    gameSeoName = StringField()
    groupId = IntField()
    history = DateTimeField()
    idProduct = IntField()
    image = StringField()
    imageCount = IntField()
    imageUrl = StringField()
    isSealed = BooleanField()
    isSingle = BooleanField()
    lastupdated = DateTimeField()
    modifiedOn = StringField()
    name = StringField()
    number = StringField()
    priceIncreased = BooleanField()
    subtype = StringField()
    topLevelLastUpdated = StringField()
    url = StringField()
    uuid = StringField()
    priced = DateTimeField()
    lastMatched = DateTimeField()
    matched = BooleanField()
    condAbbr = StringField()
    conditionAbbreviation = StringField()
    productId = StringField()
    releasedOn = DateTimeField()
    presaleInfo = EmbeddedDocumentField(PresaleInfo)
    usPrices = ListField(EmbeddedDocumentField(Price))
    prices = ListField(EmbeddedDocumentField(Price))
    pricesUpdated = DateTimeField()
    skus = ListField(EmbeddedDocumentField(Sku))
    rarity = StringField()
    description = StringField()
    ximilar_uploaded = BooleanField()
    dateAdded = DateTimeField()
    metafieldGames = ListField(StringField())
    metafieldLegalities = DictField()
    cmSinglesId = StringField()

    meta = {
        'collection': 'catalog',
        'indexes': [
            'gameName',
            'expansionName',
            'name'
        ]
    }
