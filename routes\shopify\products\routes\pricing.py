import logging
import time
from datetime import datetime
from flask import request, jsonify, Response
from flask_login import login_required, current_user
from bson import ObjectId
import json
import shautopricing

from ..config import (
    shopify_collection,
    user_collection,
    tcgplayer_key_collection
)
from ..utils import (
    calculate_new_price,
    get_shopify_headers,
    get_shopify_store_url,
    create_shopify_session,
    update_product_variants
)

logger = logging.getLogger(__name__)

@login_required
def reprice_all():
    """Reprice all products based on filter criteria"""
    user_profile = user_collection.find_one({'username': current_user.username})
    if not user_profile:
        return jsonify({'error': 'User profile not found'}), 404

    data = request.json
    filter_params = data.get('filterParams', {})

    # Build query based on filter parameters
    query = {'username': current_user.username}
    if filter_params.get('itemType') == 'tcg':
        query['tcgItem'] = True
    if filter_params.get('vendor'):
        query['vendor'] = filter_params['vendor']
    if filter_params.get('productType'):
        query['product_type'] = filter_params['productType']
    if filter_params.get('expansionName'):
        query['expansionName'] = filter_params['expansionName']
    if filter_params.get('searchTerm'):
        query['title'] = {'$regex': filter_params['searchTerm'], '$options': 'i'}
    if filter_params.get('inStockOnly'):
        query['variants.inventory_quantity'] = {'$gt': 0}
    if filter_params.get('soldOnly'):
        query['last_sold_date'] = {'$exists': True}

    # Get filtered products using aggregation pipeline
    pipeline = [
        {'$match': query},
        {'$project': {
            '_id': 1,
            'title': 1,
            'variants': 1,
            'id': 1,
            'vendor': 1,
            'product_type': 1,
            'expansionName': 1,
            'productId': 1
        }}
    ]
    
    products = list(shopify_collection.aggregate(pipeline))
    total_products = len(products)
    
    def generate():
        for i, product in enumerate(products):
            try:
                # Initialize shautopricing collections
                from .shopify_product_creator import init_shautopricing
                init_shautopricing()
                
                # Get TCGPlayer API key
                tcgplayer_key = tcgplayer_key_collection.find_one()
                if not tcgplayer_key or 'latestKey' not in tcgplayer_key:
                    logger.error("TCGPlayer API key not found in database")
                    raise Exception("TCGPlayer API key not found")
                
                # Calculate new prices using shautopricing
                old_variants = product['variants']
                new_variants, price_changes = shautopricing.process_single_product(
                    product,
                    current_user.username,
                    tcgplayer_key['latestKey']
                )
                
                # Only update MongoDB without marking for Shopify sync
                shopify_collection.update_one(
                    {'_id': product['_id']},
                    {'$set': {
                        'variants': new_variants,
                        'last_repriced': datetime.utcnow()
                    }}
                )
                
                # Yield progress update
                progress_data = {
                    'progress': (i + 1) / total_products,
                    'processed': i + 1,
                    'total': total_products,
                    'status': 'success',
                    'message': f"Updated {product['title']}" + 
                             (f" with {len(price_changes)} price changes" if price_changes else " (no changes needed)")
                }
                yield f"data: {json.dumps(progress_data)}\n\n"
                
                # Rate limiting
                time.sleep(0.5)
                    
            except Exception as e:
                logger.error(f"Error processing product {product.get('title')}: {str(e)}")
                # Yield error update
                progress_data = {
                    'progress': (i + 1) / total_products,
                    'processed': i + 1,
                    'total': total_products,
                    'status': 'error',
                    'message': f"Failed to update {product.get('title', 'Unknown')}: {str(e)}"
                }
                yield f"data: {json.dumps(progress_data)}\n\n"
                continue

    return Response(generate(), mimetype='text/event-stream')

@login_required
def manual_reprice():
    """Manually reprice a product using TCGPlayer prices"""
    data = request.json
    product_id = data.get('productId')
    variants = data.get('variants', [])
    tcgplayer_prices = data.get('tcgplayerPrices', {})
    
    if not product_id:
        return jsonify({'error': 'Product ID is required'}), 400
    if not variants:
        return jsonify({'error': 'At least one variant must be selected'}), 400
    if not tcgplayer_prices:
        return jsonify({'error': 'TCGPlayer prices are required'}), 400

    try:
        product = shopify_collection.find_one({
            '_id': ObjectId(product_id),
            'username': current_user.username
        })
        if not product:
            return jsonify({'error': 'Product not found'}), 404

        user_profile = user_collection.find_one({'username': current_user.username})
        if not user_profile:
            return jsonify({'error': 'User profile not found'}), 404

        # Initialize shautopricing collections
        from .shopify_product_creator import init_shautopricing
        init_shautopricing()
        
        # Get TCGPlayer API key
        tcgplayer_key = tcgplayer_key_collection.find_one()
        if not tcgplayer_key or 'latestKey' not in tcgplayer_key:
            logger.error("TCGPlayer API key not found in database")
            raise Exception("TCGPlayer API key not found")

        # Process each selected variant
        updated_variants = product['variants'].copy()
        price_changes = []
        
        for variant_info in variants:
            variant_id = variant_info['variantId']
            condition = variant_info['condition']
            printing_type = variant_info['printingType']
            
            # Find the variant in the product
            variant_index = next((i for i, v in enumerate(updated_variants) if str(v['id']) == str(variant_id)), None)
            if variant_index is None:
                continue
                
            # Get TCGPlayer prices for this printing type
            tcg_prices = tcgplayer_prices.get(printing_type, {})
            if not tcg_prices:
                continue
                
            # Format pricing info
            pricing_info = {}
            
            # Get market price first as it's often the most reliable
            market_price = tcg_prices.get('marketPrice')
            if market_price is not None:
                pricing_info['marketPrice'] = float(market_price)
            
            # Get low price
            low_price = tcg_prices.get('lowPrice')
            if low_price is not None:
                pricing_info['lowPrice'] = float(low_price)
            
            # Get mid price (fallback to market price if not available)
            mid_price = tcg_prices.get('midPrice')
            if mid_price is None and market_price is not None:
                mid_price = market_price
            if mid_price is not None:
                pricing_info['midPrice'] = float(mid_price)
            
            # Get high price (fallback to market price if not available)
            high_price = tcg_prices.get('highPrice')
            if high_price is None and market_price is not None:
                high_price = market_price
            if high_price is not None:
                pricing_info['highPrice'] = float(high_price)
            
            if not pricing_info:
                continue
                
            # Get pricing rules
            pricing_rules = user_profile.get('customStepping', {
                'nm': 100, 'lp': 90, 'mp': 85, 'hp': 75, 'dm': 65
            })
            
            # Prepare settings with custom stepping rules
            from routes.warehouse.utils import prepare_shopify_settings
            settings = prepare_shopify_settings(current_user.username)
            
            # Override customStepping with the user's pricing rules
            settings['customStepping'] = pricing_rules
            min_price = settings.get('minPrice', 0)
            
            # Create variant info for price calculation
            variant_info = {
                'id': variant_id,
                'title': updated_variants[variant_index]['title'],
                'option1': condition,
                'price': updated_variants[variant_index]['price']
            }
            
            # Calculate new price using the same logic as automated repricing
            from pricing_utils import PricingCalculator
            calculator = PricingCalculator(settings, user_profile.get('currency', 'USD'))
            
            # Prepare sku_info
            sku_info = {
                'pricingInfo': pricing_info,
                'condName': condition,
                'printingName': printing_type,
                'skuId': variant_id,
                'variantTitle': variant_info['title']
            }
            
            # Calculate price
            new_price, is_missing, _ = calculator.calculate_final_price(sku_info, product)
            
            if is_missing or new_price is None:
                continue
            
            # Record price change if different
            old_price = float(updated_variants[variant_index]['price'])
            if abs(new_price - old_price) > 0.01:  # Check if price change is significant
                price_changes.append({
                    'variant_id': variant_id,
                    'variant_title': updated_variants[variant_index]['title'],
                    'old_price': old_price,
                    'new_price': new_price
                })
                updated_variants[variant_index]['price'] = str(new_price)

        # Only update MongoDB and mark for pushing to Shopify later
        shopify_collection.update_one(
            {'_id': ObjectId(product_id)},
            {'$set': {
                'variants': updated_variants,
                'last_repriced': datetime.utcnow(),
                'needsManual': False
            }}
        )

        if price_changes:
            return jsonify({
                'message': 'Successfully repriced product with manual printing type selection',
                'price_changes': price_changes
            })
        else:
            return jsonify({
                'message': 'No price changes needed for the selected printing type'
            })

    except Exception as e:
        logger.error(f"Error during manual repricing: {str(e)}")
        return jsonify({'error': f'An error occurred during manual repricing: {str(e)}'}), 500

@login_required
def test_settings():
    """Test current pricing settings on a random product"""
    try:
        # Get a random product for the current user
        pipeline = [
            {'$match': {'username': current_user.username}},
            {'$sample': {'size': 1}}
        ]
        
        product = next(shopify_collection.aggregate(pipeline), None)
        if not product:
            return jsonify({'error': 'No products found'}), 404

        # Store original variant prices
        original_variants = product['variants']
        original_prices = {str(v['id']): float(v['price']) for v in original_variants}

        # Initialize shautopricing collections
        from .shopify_product_creator import init_shautopricing
        init_shautopricing()
        
        # Get TCGPlayer API key
        tcgplayer_key = tcgplayer_key_collection.find_one()
        if not tcgplayer_key or 'latestKey' not in tcgplayer_key:
            logger.error("TCGPlayer API key not found in database")
            raise Exception("TCGPlayer API key not found")

        # Calculate new prices using shautopricing
        new_variants, price_changes = shautopricing.process_single_product(
            product,
            current_user.username,
            tcgplayer_key['latestKey']
        )

        # Calculate price differences
        for change in price_changes:
            change['difference'] = round(change['new_price'] - change['old_price'], 2)
            change['difference_percent'] = round((change['new_price'] - change['old_price']) / change['old_price'] * 100, 1)

        # Get user settings for response
        user_profile = user_collection.find_one({'username': current_user.username})
        if not user_profile:
            return jsonify({'error': 'User profile not found'}), 404

        response_data = {
            'message': 'Test pricing completed',
            'product': {
                'title': product['title'],
                'image': product.get('image', {}).get('src')
            },
            'price_changes': price_changes if price_changes else [],
            'settings_used': {
                'custom_stepping': user_profile.get('customStepping', {
                    'nm': 100, 'lp': 90, 'mp': 85, 'hp': 75, 'dm': 65
                }),
                'min_price': user_profile.get('minPrice', 0.25),
                'currency': user_profile.get('currency', 'USD')
            }
        }

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error testing settings: {str(e)}")
        return jsonify({'error': f'An error occurred while testing settings: {str(e)}'}), 500
