import requests
import logging
from datetime import datetime, timedelta
from threading import Lock

logger = logging.getLogger(__name__)

EXCHANGE_RATE_API_KEY = "9a84a0b27c0a21980d122046"

class CurrencyCache:
    def __init__(self, ttl_hours=24):
        self.cache = {}
        self.ttl = timedelta(hours=ttl_hours)
        self.lock = Lock()
        self.last_cleanup = datetime.utcnow()

    def get(self, currency):
        with self.lock:
            if currency in self.cache:
                rate_data = self.cache[currency]
                if datetime.utcnow() - rate_data['timestamp'] < self.ttl:
                    return rate_data['rate']
                else:
                    del self.cache[currency]
            return None

    def set(self, currency, rate):
        with self.lock:
            self.cache[currency] = {
                'rate': rate,
                'timestamp': datetime.utcnow()
            }

    def clear_expired(self):
        with self.lock:
            current_time = datetime.utcnow()
            expired = [currency for currency, data in self.cache.items() 
                      if current_time - data['timestamp'] >= self.ttl]
            for currency in expired:
                del self.cache[currency]
            self.last_cleanup = current_time

# Initialize global currency cache
currency_cache = CurrencyCache()

def get_exchange_rate(target_currency):
    """
    Get exchange rate with caching and error handling
    """
    if target_currency == 'USD':
        return 1.0
    
    # Check cache first
    cached_rate = currency_cache.get(target_currency)
    if cached_rate is not None:
        logger.debug(f"Using cached exchange rate for {target_currency}: {cached_rate}")
        return cached_rate
    
    try:
        url = f"https://v6.exchangerate-api.com/v6/{EXCHANGE_RATE_API_KEY}/latest/USD"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        rates = response.json().get('conversion_rates', {})
        rate = rates.get(target_currency)
        
        if rate is None:
            logger.error(f"Exchange rate not found for {target_currency}, using 1.0")
            return 1.0
            
        # Cache the new rate
        currency_cache.set(target_currency, rate)
        logger.info(f"Fetched and cached new exchange rate for {target_currency}: {rate}")
        return rate
    except Exception as e:
        logger.error(f"Error fetching exchange rate for {target_currency}: {str(e)}")
        return 1.0

def convert_price_to_currency(price_usd, target_currency):
    """
    Convert a USD price to the target currency
    """
    if not price_usd:
        return None
        
    exchange_rate = get_exchange_rate(target_currency)
    return round(float(price_usd) * exchange_rate, 2)

def convert_prices_to_currency(prices, target_currency):
    """
    Convert a dictionary of USD prices to the target currency
    """
    if not prices:
        return {}
        
    exchange_rate = get_exchange_rate(target_currency)
    converted_prices = {}
    
    for key, price in prices.items():
        if price is not None:
            converted_prices[key] = round(float(price) * exchange_rate, 2)
        else:
            converted_prices[key] = None
            
    return converted_prices
