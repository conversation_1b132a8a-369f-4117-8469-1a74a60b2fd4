from typing import Dict, List, Optional
from datetime import datetime
from models.database import mongo
from config import Config

class EbayPoliciesModel:
    """Model for managing eBay business policies"""
    
    def __init__(self):
        self.db = mongo.get_connection().get_database(Config.MONGO_DBNAME)
        self.collection = self.db['ebayPolicies']

    def save_policies(self, user_id: str, policies: Dict[str, str]) -> bool:
        """Save business policies for a user"""
        try:
            self.collection.update_one(
                {'user_id': user_id},
                {
                    '$set': {
                        'policies': policies,
                        'last_updated': datetime.utcnow()
                    }
                },
                upsert=True
            )
            return True
        except Exception as e:
            print(f"Error saving policies: {str(e)}")
            return False

    def get_policies(self, user_id: str) -> Dict[str, List[Dict[str, str]]]:
        """Get business policies for a user"""
        try:
            doc = self.collection.find_one({'user_id': user_id})
            if doc and 'policies' in doc:
                return {
                    'shipping': [
                        {'id': doc['policies'].get('shipping', ''), 'name': 'Default Shipping Policy'}
                    ],
                    'return': [
                        {'id': doc['policies'].get('return', ''), 'name': 'Default Return Policy'}
                    ],
                    'payment': [
                        {'id': doc['policies'].get('payment', ''), 'name': 'Default Payment Policy'}
                    ]
                }
            return {
                'shipping': [],
                'return': [],
                'payment': []
            }
        except Exception as e:
            print(f"Error getting policies: {str(e)}")
            return {
                'shipping': [],
                'return': [],
                'payment': []
            }

    def delete_policies(self, user_id: str) -> bool:
        """Delete business policies for a user"""
        try:
            result = self.collection.delete_one({'user_id': user_id})
            return result.deleted_count > 0
        except Exception as e:
            print(f"Error deleting policies: {str(e)}")
            return False
