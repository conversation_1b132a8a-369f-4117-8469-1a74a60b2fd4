from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
import re
from routes.buylist.core import logger, catalog_collection, db

hotlist_bp = Blueprint('hotlist', __name__, url_prefix='/buylist')

@hotlist_bp.route('/hotlist')
@login_required
def hotlist_page():
    """Render the hotlist page."""
    return render_template('hotlist.html')

@hotlist_bp.route('/api/hotlist/games')
@login_required
def get_games():
    """Get a list of distinct games from the catalog."""
    try:
        games = catalog_collection.distinct('gameName')
        games = sorted([g for g in games if g])
        return jsonify({'games': games})
    except Exception as e:
        logger.error(f"Error fetching games: {str(e)}", exc_info=True)
        return jsonify({'error': 'Failed to fetch games'}), 500

@hotlist_bp.route('/api/hotlist/expansions')
@login_required
def get_expansions():
    """Get a list of distinct expansions for a specific game."""
    game = request.args.get('game')
    if not game:
        return jsonify({'error': 'Missing game parameter'}), 400
    
    try:
        expansions = catalog_collection.distinct('expansionName', {'gameName': game})
        expansions = sorted([e for e in expansions if e])
        return jsonify({'expansions': expansions})
    except Exception as e:
        logger.error(f"Error fetching expansions for game {game}: {str(e)}", exc_info=True)
        return jsonify({'error': 'Failed to fetch expansions'}), 500

@hotlist_bp.route('/api/hotlist/records')
@login_required
def get_records():
    """Get records for a specific game and expansion."""
    game = request.args.get('game')
    expansion = request.args.get('expansion')
    
    if not game or not expansion:
        return jsonify({'error': 'Missing game or expansion parameter'}), 400
    
    try:
        query = {'gameName': game, 'expansionName': expansion}
        projection = {
            '_id': 0,
            'name': 1,
            'expansionName': 1,
            'number': 1,
            'productId': 1,
            'rarity': 1,
            'image': 1
        }
        records = list(catalog_collection.find(query, projection).limit(500))
        return jsonify({'records': records})
    except Exception as e:
        logger.error(f"Error fetching records for game {game} and expansion {expansion}: {str(e)}", exc_info=True)
        return jsonify({'error': 'Failed to fetch records'}), 500

@hotlist_bp.route('/api/hotlist/search')
@login_required
def search_records():
    """Search for records by name and optionally by game."""
    search_term = request.args.get('term')
    game = request.args.get('game')
    
    if not search_term:
        return jsonify({'error': 'Missing search term'}), 400
    
    try:
        # Create a case-insensitive regex pattern for the search term
        pattern = re.compile(f'.*{re.escape(search_term)}.*', re.IGNORECASE)
        query = {'name': pattern}
        
        # Add game filter if provided
        if game:
            query['gameName'] = game
            
        projection = {
            '_id': 0,
            'name': 1,
            'expansionName': 1,
            'number': 1,
            'productId': 1,
            'rarity': 1,
            'image': 1,
            'gameName': 1
        }
        records = list(catalog_collection.find(query, projection).limit(100))
        return jsonify({'records': records})
    except Exception as e:
        logger.error(f"Error searching records for term {search_term}: {str(e)}", exc_info=True)
        return jsonify({'error': 'Failed to search records'}), 500

@hotlist_bp.route('/api/hotlist/user-hotlist')
@login_required
def get_user_hotlist():
    """Get the user's hotlist."""
    try:
        # Get the user's hotlist
        hotlist_doc = db.hotlist.find_one({'username': current_user.username})
        
        if not hotlist_doc:
            # Create a new hotlist for the user if it doesn't exist
            hotlist_doc = {
                'username': current_user.username,
                'markup': 0,  # Default markup percentage
                'hotlist': []  # Empty array of productIds
            }
            db.hotlist.insert_one(hotlist_doc)
            return jsonify({'hotlist': [], 'markup': 0})
        
        # Get the product details for each productId in the hotlist
        product_ids = hotlist_doc.get('hotlist', [])
        
        if not product_ids:
            return jsonify({'hotlist': [], 'markup': hotlist_doc.get('markup', 0)})
        
        # Convert string productIds to integers for MongoDB query
        product_ids_int = []
        for pid in product_ids:
            try:
                product_ids_int.append(int(pid))
            except (ValueError, TypeError):
                logger.warning(f"Invalid productId format: {pid}")
        
        # Get product details from catalog collection
        projection = {
            '_id': 0,
            'name': 1,
            'expansionName': 1,
            'number': 1,
            'productId': 1,
            'rarity': 1,
            'image': 1,
            'gameName': 1
        }
        
        products = list(catalog_collection.find(
            {'productId': {'$in': product_ids_int}},
            projection
        ))
        
        return jsonify({
            'hotlist': products,
            'markup': hotlist_doc.get('markup', 0)
        })
    except Exception as e:
        logger.error(f"Error fetching user hotlist: {str(e)}", exc_info=True)
        return jsonify({'error': 'Failed to fetch hotlist'}), 500

@hotlist_bp.route('/api/hotlist/add', methods=['POST'])
@login_required
def add_to_hotlist():
    """Add a product to the user's hotlist."""
    try:
        data = request.json
        if not data or 'productId' not in data:
            return jsonify({'error': 'Missing productId parameter'}), 400
        
        product_id = str(data['productId'])
        
        # Update the user's hotlist
        result = db.hotlist.update_one(
            {'username': current_user.username},
            {
                '$addToSet': {'hotlist': product_id},
                '$setOnInsert': {'markup': 0}
            },
            upsert=True
        )
        
        if result.modified_count > 0 or result.upserted_id:
            return jsonify({'success': True, 'message': 'Product added to hotlist'})
        else:
            return jsonify({'success': False, 'message': 'Product already in hotlist'})
    except Exception as e:
        logger.error(f"Error adding to hotlist: {str(e)}", exc_info=True)
        return jsonify({'error': 'Failed to add to hotlist'}), 500

@hotlist_bp.route('/api/hotlist/remove', methods=['POST'])
@login_required
def remove_from_hotlist():
    """Remove a product from the user's hotlist."""
    try:
        data = request.json
        if not data or 'productId' not in data:
            return jsonify({'error': 'Missing productId parameter'}), 400
        
        product_id = str(data['productId'])
        
        # Update the user's hotlist
        result = db.hotlist.update_one(
            {'username': current_user.username},
            {'$pull': {'hotlist': product_id}}
        )
        
        if result.modified_count > 0:
            return jsonify({'success': True, 'message': 'Product removed from hotlist'})
        else:
            return jsonify({'success': False, 'message': 'Product not found in hotlist'})
    except Exception as e:
        logger.error(f"Error removing from hotlist: {str(e)}", exc_info=True)
        return jsonify({'error': 'Failed to remove from hotlist'}), 500

@hotlist_bp.route('/api/hotlist/update-markup', methods=['POST'])
@login_required
def update_markup():
    """Update the markup percentage for the user's hotlist."""
    try:
        data = request.json
        if not data or 'markup' not in data:
            return jsonify({'error': 'Missing markup parameter'}), 400
        
        try:
            markup = float(data['markup'])
        except (ValueError, TypeError):
            return jsonify({'error': 'Invalid markup value'}), 400
        
        # Update the user's markup
        result = db.hotlist.update_one(
            {'username': current_user.username},
            {'$set': {'markup': markup}},
            upsert=True
        )
        
        if result.modified_count > 0 or result.upserted_id:
            return jsonify({'success': True, 'message': 'Markup updated successfully'})
        else:
            return jsonify({'success': True, 'message': 'No changes to markup'})
    except Exception as e:
        logger.error(f"Error updating markup: {str(e)}", exc_info=True)
        return jsonify({'error': 'Failed to update markup'}), 500
