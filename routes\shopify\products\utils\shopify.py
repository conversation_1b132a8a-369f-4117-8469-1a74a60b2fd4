import requests
import logging
from typing import Dict, List, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)

def get_shopify_headers(access_token: str) -> Dict[str, str]:
    """
    Get headers for Shopify API requests
    """
    return {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": access_token
    }

def get_shopify_location_id(store_name: str, access_token: str, db) -> Optional[str]:
    """
    Get Shopify location ID with caching
    """
    try:
        # Check cache first
        location_cache = db.get_collection('location_cache')
        cached_location = location_cache.find_one({'store_name': store_name})
        
        if cached_location and cached_location.get('location_id'):
            return cached_location['location_id']
            
        # Get location ID from Shopify
        headers = get_shopify_headers(access_token)
        locations_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/locations.json"
        locations_response = requests.get(locations_url, headers=headers)
        locations_response.raise_for_status()
        locations = locations_response.json()['locations']
        
        if not locations:
            logger.error(f"No locations found for store {store_name}")
            return None
            
        location_id = locations[0]['id']
        
        # Cache the location ID
        location_cache.update_one(
            {'store_name': store_name},
            {'$set': {
                'location_id': location_id,
                'last_updated': datetime.utcnow()
            }},
            upsert=True
        )
        
        return location_id
        
    except Exception as e:
        logger.error(f"Error getting Shopify location ID: {str(e)}")
        return None

def update_shopify_product(store_name: str, access_token: str, product_id: str, 
                          variants: List[Dict[str, Union[str, float, int]]]) -> bool:
    """
    Update a Shopify product's variants
    """
    try:
        headers = get_shopify_headers(access_token)
        url = f"https://{store_name}.myshopify.com/admin/api/2023-04/products/{product_id}.json"
        
        product_data = {
            "product": {
                "id": product_id,
                "variants": [
                    {
                        "id": variant['id'],
                        "price": str(variant['price'])
                    } for variant in variants
                ]
            }
        }
        
        response = requests.put(url, headers=headers, json=product_data)
        response.raise_for_status()
        return True
        
    except Exception as e:
        logger.error(f"Error updating Shopify product {product_id}: {str(e)}")
        return False

def update_shopify_inventory(store_name: str, access_token: str, location_id: str,
                           inventory_updates: List[Dict[str, Union[str, int]]]) -> bool:
    """
    Update Shopify inventory levels in batches
    """
    try:
        headers = get_shopify_headers(access_token)
        inventory_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/inventory_levels/set.json"
        
        # Process inventory updates in batches of 50
        for i in range(0, len(inventory_updates), 50):
            batch = inventory_updates[i:i+50]
            for inventory_data in batch:
                response = requests.post(inventory_url, headers=headers, json={
                    "location_id": location_id,
                    "inventory_item_id": inventory_data['inventory_item_id'],
                    "available": inventory_data['available']
                })
                response.raise_for_status()
                
        return True
        
    except Exception as e:
        logger.error(f"Error updating Shopify inventory: {str(e)}")
        return False

def delete_shopify_product(store_name: str, access_token: str, product_id: str) -> bool:
    """
    Delete a product from Shopify
    """
    try:
        headers = get_shopify_headers(access_token)
        url = f"https://{store_name}.myshopify.com/admin/api/2023-04/products/{product_id}.json"
        
        response = requests.delete(url, headers=headers)
        response.raise_for_status()
        return True
        
    except Exception as e:
        logger.error(f"Error deleting Shopify product {product_id}: {str(e)}")
        return False
