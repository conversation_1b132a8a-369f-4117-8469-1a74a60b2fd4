from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models.shproducts_model import ShProducts
from models.pricing_model import PricingRule, PricingHistory, PricingAnalytics, BulkPricingJob
from datetime import datetime, timedelta
import json
import uuid

pricing_bp = Blueprint('pricing', __name__, url_prefix='/pricing')

@pricing_bp.route('/dashboard')
@login_required
def pricing_dashboard():
    """Main pricing dashboard page"""
    try:
        # Get basic pricing statistics
        total_products = ShProducts.objects.count()

        # Get products with advanced pricing enabled
        products_with_custom_pricing = ShProducts.objects(uses_advanced_pricing=True).count()

        # Get recent pricing changes (last 30 days)
        thirty_days_ago = datetime.now() - timedelta(days=30)
        recent_pricing_changes = PricingHistory.objects(updated_at__gte=thirty_days_ago).count()

        # Calculate average price from variants
        all_products = ShProducts.objects()
        total_price = 0
        price_count = 0

        for product in all_products:
            if product.variants:
                for variant in product.variants:
                    if variant.get('price'):
                        try:
                            price = float(variant['price'])
                            total_price += price
                            price_count += 1
                        except (ValueError, TypeError):
                            continue

        avg_price = round(total_price / price_count, 2) if price_count > 0 else 0

        # Get products by price range (analyzing variants)
        price_ranges = {
            'under_5': 0,
            'between_5_20': 0,
            'between_20_50': 0,
            'over_50': 0
        }

        for product in all_products:
            if product.variants:
                for variant in product.variants:
                    if variant.get('price'):
                        try:
                            price = float(variant['price'])
                            if price < 5:
                                price_ranges['under_5'] += 1
                            elif 5 <= price < 20:
                                price_ranges['between_5_20'] += 1
                            elif 20 <= price < 50:
                                price_ranges['between_20_50'] += 1
                            else:
                                price_ranges['over_50'] += 1
                        except (ValueError, TypeError):
                            continue

        # Get recent pricing history
        recent_history = PricingHistory.objects().order_by('-updated_at')[:20]

        return render_template('pricing/dashboard.html',
                             total_products=total_products,
                             products_with_custom_pricing=products_with_custom_pricing,
                             recent_pricing_changes=recent_pricing_changes,
                             avg_price=avg_price,
                             price_ranges=price_ranges,
                             recent_history=recent_history)

    except Exception as e:
        flash(f'Error loading pricing dashboard: {str(e)}', 'error')
        return redirect(url_for('main.dashboard'))

@pricing_bp.route('/bulk-update')
@login_required
def bulk_update():
    """Bulk pricing update page"""
    return render_template('pricing/bulk_update.html')

@pricing_bp.route('/rules')
@login_required
def pricing_rules():
    """Pricing rules management page"""
    rules = PricingRule.objects().order_by('-created_at')
    return render_template('pricing/rules.html', rules=rules)

@pricing_bp.route('/history')
@login_required
def pricing_history():
    """Pricing history page"""
    page = request.args.get('page', 1, type=int)
    per_page = 50

    # Calculate skip value for pagination
    skip = (page - 1) * per_page

    history = PricingHistory.objects().order_by('-updated_at').skip(skip).limit(per_page)
    total_count = PricingHistory.objects().count()

    # Create pagination info
    pagination_info = {
        'page': page,
        'per_page': per_page,
        'total': total_count,
        'has_prev': page > 1,
        'has_next': skip + per_page < total_count,
        'prev_num': page - 1 if page > 1 else None,
        'next_num': page + 1 if skip + per_page < total_count else None
    }

    return render_template('pricing/history.html', history=history, pagination=pagination_info)

@pricing_bp.route('/api/update-price', methods=['POST'])
@login_required
def update_price():
    """API endpoint to update product price"""
    try:
        data = request.get_json()
        product_id = data.get('product_id')
        variant_id = data.get('variant_id')
        new_price = float(data.get('new_price'))
        reason = data.get('reason', 'Manual update')

        # Find the product
        product = ShProducts.objects(productId=product_id).first()
        if not product:
            return jsonify({
                'success': False,
                'message': 'Product not found'
            }), 404

        # Find and update the specific variant
        old_price = None
        variant_updated = False

        for variant in product.variants:
            if variant.get('id') == variant_id:
                old_price = float(variant.get('price', 0))
                variant['price'] = str(new_price)
                variant_updated = True
                break

        if not variant_updated:
            return jsonify({
                'success': False,
                'message': 'Variant not found'
            }), 404

        # Save the product
        product.save()

        # Create pricing history record
        history = PricingHistory(
            product_id=product_id,
            product_name=product.title,
            vendor=product.vendor,
            old_price=old_price,
            new_price=new_price,
            change_reason='manual_update',
            change_description=reason,
            updated_by=current_user.username,
            updated_at=datetime.now()
        )
        history.save()

        return jsonify({
            'success': True,
            'message': f'Price updated from £{old_price} to £{new_price}'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error updating price: {str(e)}'
        }), 400

@pricing_bp.route('/api/bulk-update', methods=['POST'])
@login_required
def api_bulk_update():
    """API endpoint for bulk price updates"""
    try:
        data = request.get_json()
        update_type = data.get('update_type')  # 'percentage', 'fixed_amount', 'set_price'
        value = float(data.get('value'))
        filters = data.get('filters', {})
        reason = data.get('reason', 'Bulk update')

        # Build MongoDB query based on filters
        query_filters = {}

        if filters.get('vendor'):
            query_filters['vendor'] = filters['vendor']
        if filters.get('product_type'):
            query_filters['product_type'] = filters['product_type']

        products = ShProducts.objects(**query_filters)
        updated_count = 0

        for product in products:
            if not product.variants:
                continue

            product_updated = False

            for variant in product.variants:
                if not variant.get('price'):
                    continue

                try:
                    old_price = float(variant['price'])

                    # Apply price filters if specified
                    if filters.get('min_price') and old_price < float(filters['min_price']):
                        continue
                    if filters.get('max_price') and old_price > float(filters['max_price']):
                        continue

                    # Calculate new price
                    if update_type == 'percentage':
                        new_price = old_price * (1 + value / 100)
                    elif update_type == 'fixed_amount':
                        new_price = old_price + value
                    elif update_type == 'set_price':
                        new_price = value
                    else:
                        continue

                    # Ensure price is not negative
                    new_price = max(0.01, new_price)
                    new_price = round(new_price, 2)

                    # Update variant price
                    variant['price'] = str(new_price)
                    product_updated = True

                    # Create history record
                    history = PricingHistory(
                        product_id=product.productId,
                        product_name=product.title,
                        vendor=product.vendor,
                        old_price=old_price,
                        new_price=new_price,
                        change_reason='bulk_update',
                        change_description=reason,
                        updated_by=current_user.username,
                        updated_at=datetime.now()
                    )
                    history.save()

                except (ValueError, TypeError):
                    continue

            if product_updated:
                product.save()
                updated_count += 1

        return jsonify({
            'success': True,
            'message': f'Successfully updated {updated_count} products'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error performing bulk update: {str(e)}'
        }), 400
