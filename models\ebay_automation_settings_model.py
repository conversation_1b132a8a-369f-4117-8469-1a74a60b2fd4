"""Model for eBay automation settings."""
from datetime import datetime
from typing import Optional, List, Dict
from bson import ObjectId
from pymongo.database import Database
from pymongo.collection import Collection

class EbayAutomationSettings:
    def __init__(self, db: Database):
        self.collection: Collection = db['ebay_automation_settings']
        self.setup_indexes()

    def setup_indexes(self):
        """Setup required indexes for the collection."""
        self.collection.create_index('user_id', unique=True)
        self.collection.create_index('updated_at')

    def get_settings(self, user_id: str) -> Optional[dict]:
        """Get eBay automation settings for a user."""
        return self.collection.find_one({'user_id': user_id})

    def save_settings(self, user_id: str, settings: dict) -> bool:
        """Save or update eBay automation settings for a user."""
        try:
            now = datetime.utcnow()
            
            # Validate settings
            if 'floor_price' in settings and not isinstance(settings['floor_price'], (int, float)):
                raise ValueError("Floor price must be a number")
            
            if 'price_modifier' in settings and not isinstance(settings['price_modifier'], (int, float)):
                raise ValueError("Price modifier must be a number")
            
            if 'product_types' in settings and not isinstance(settings['product_types'], list):
                raise ValueError("Product types must be a list")
            
            # Prepare settings document
            settings_doc = {
                'user_id': user_id,
                'floor_price': float(settings.get('floor_price', 0)),
                'price_modifier': float(settings.get('price_modifier', 0)),
                'product_types': settings.get('product_types', []),
                'updated_at': now
            }

            result = self.collection.update_one(
                {'user_id': user_id},
                {'$set': settings_doc, '$setOnInsert': {'created_at': now}},
                upsert=True
            )
            
            return bool(result.modified_count or result.upserted_id)
            
        except Exception as e:
            raise Exception(f"Failed to save eBay automation settings: {str(e)}")

    def delete_settings(self, user_id: str) -> bool:
        """Delete eBay automation settings for a user."""
        result = self.collection.delete_one({'user_id': user_id})
        return result.deleted_count > 0
