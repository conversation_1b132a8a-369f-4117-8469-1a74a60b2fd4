/**
 * Dashboard JavaScript
 * Handles dashboard functionality including lazy loading, tab switching, and other interactions
 * Implements progressive loading for better performance
 */

// Track loaded components
const loadedComponents = {
    quickAccess: false,
    tabs: false,
    yesterdayTab: false,
    enterpriseFeatures: false
};

// Main initialization function
document.addEventListener('DOMContentLoaded', function() {
    // Show loading indicator
    showPageLoadingIndicator();

    // Initialize critical components first
    initCriticalComponents();

    // Defer non-critical components
    setTimeout(initNonCriticalComponents, 100);
});

/**
 * Initialize critical components that should load immediately
 */
function initCriticalComponents() {
    console.log('Initializing critical components...');

    // Initialize quick access section (most important for navigation)
    initQuickAccessSection();
    loadedComponents.quickAccess = true;

    // Initialize tab structure and load yesterday tab content
    initDashboardTabs();
    loadedComponents.tabs = true;

    // Load yesterday tab content immediately since it's the default
    loadYesterdayTab();
    loadedComponents.yesterdayTab = true;

    // Hide loading indicator when critical components are loaded
    hidePageLoadingIndicator();

    // Log performance metrics
    console.log('Critical components loaded in:', performance.now(), 'ms');
}

/**
 * Initialize non-critical components that can be deferred
 */
function initNonCriticalComponents() {
    console.log('Initializing non-critical components...');

    // Initialize enterprise features (modals, forms, etc.)
    setTimeout(() => {
        initEnterpriseFeatures();
        loadedComponents.enterpriseFeatures = true;
        console.log('Enterprise features loaded');
    }, 200);

    // Fix select dropdown styling for dark mode
    setTimeout(() => {
        initDarkModeSelects();
        console.log('Dark mode selects initialized');
    }, 300);

    // Log performance metrics
    console.log('All components loaded in:', performance.now(), 'ms');
}

/**
 * Show loading indicator while page is loading
 */
function showPageLoadingIndicator() {
    // Create loading overlay if it doesn't exist
    if (!document.getElementById('dashboard-loading-overlay')) {
        const overlay = document.createElement('div');
        overlay.id = 'dashboard-loading-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.3s ease;
        `;

        const spinner = document.createElement('div');
        spinner.className = 'loading-spinner';
        spinner.style.cssText = `
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        `;

        // Add keyframes for spinner animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        overlay.appendChild(spinner);
        document.body.appendChild(overlay);
    }
}

/**
 * Hide loading indicator when critical components are loaded
 */
function hidePageLoadingIndicator() {
    const overlay = document.getElementById('dashboard-loading-overlay');
    if (overlay) {
        overlay.style.opacity = '0';
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    }
}

/**
 * Initialize dashboard tabs with lazy loading
 */
function initDashboardTabs() {
    const tabButtons = document.querySelectorAll('.dashboard-tab-btn');
    const tabContents = document.querySelectorAll('.dashboard-tab-content');

    // Track loaded tabs
    const loadedTabs = {
        'yesterday': true // Yesterday tab is loaded by default (active tab)
    };

    // Loading indicators for each tab
    const loadingIndicators = {
        'yesterday': '<div class="loading-indicator"><i class="fas fa-spinner fa-spin"></i> Loading yesterday\'s data...</div>'
    };

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Get the tab ID
            const tabId = button.getAttribute('data-tab');
            const tabContent = document.getElementById(`${tabId}-tab`);

            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked button
            button.classList.add('active');

            // If tab content is not loaded yet, load it
            if (!loadedTabs[tabId]) {
                // Show loading indicator
                tabContent.innerHTML = loadingIndicators[tabId];
                tabContent.classList.add('active');

                // Load tab content asynchronously
                loadTabContent(tabId).then(() => {
                    loadedTabs[tabId] = true;
                }).catch(error => {
                    console.error(`Error loading ${tabId} tab:`, error);
                    tabContent.innerHTML = `<div class="alert alert-danger">Error loading content. Please try again.</div>`;
                });
            } else {
                // Show the tab content
                tabContent.classList.add('active');
            }
        });
    });
}

/**
 * Load tab content asynchronously
 * @param {string} tabId - The ID of the tab to load
 * @returns {Promise} - A promise that resolves when the content is loaded
 */
function loadTabContent(tabId) {
    return new Promise((resolve, reject) => {
        // Simulate network delay for demonstration
        setTimeout(() => {
            try {
                switch (tabId) {
                    case 'yesterday':
                        loadYesterdayTab();
                        break;
                    default:
                        reject(new Error(`Unknown tab: ${tabId}`));
                        return;
                }
                resolve();
            } catch (error) {
                reject(error);
            }
        }, 100); // Small delay to show loading indicator
    });
}

/**
 * Load the Enterprise Benefits tab content
 * Note: This function is kept for backward compatibility but is no longer used
 * as the Enterprise Benefits tab has been removed
 */
function loadEnterpriseTab() {
    // Get the tab content element
    const tabContent = document.getElementById('enterprise-tab');

    // If the tab doesn't exist, just return
    if (!tabContent) {
        console.log('Enterprise tab not found in the DOM');
        return;
    }

    // Display a message that this feature is no longer available
    tabContent.innerHTML = `
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <span>Enterprise Benefits features are no longer available.</span>
        </div>
    `;
}

/**
 * Load the Yesterday tab content
 */
function loadYesterdayTab() {
    fetch('/api/dashboard/yesterday-content')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Get the tab content element
            const tabContent = document.getElementById('yesterday-tab');

            if (data.success) {
                // Get the data from the API response
                const tabData = data.data;

                // Check if the template exists
                const templateElement = document.getElementById('yesterday-tab-template');
                if (templateElement) {
                    // Get the template content
                    let content = templateElement.innerHTML;

                    // Use the template for now, but in the future we could dynamically
                    // build the content based on the API response
                    tabContent.innerHTML = content;

                    // Update the values with the data from the API
                    if (tabContent.querySelector('.order-stat-value:nth-of-type(1)')) {
                        tabContent.querySelector('.order-stat-value:nth-of-type(1)').textContent =
                            tabData.last_24_hours_orders;
                    }

                    if (tabContent.querySelector('.order-stat-value:nth-of-type(2)')) {
                        tabContent.querySelector('.order-stat-value:nth-of-type(2)').textContent =
                            tabData.last_24_hours_value.toFixed(2);
                    }

                    // Calculate and update average order value
                    if (tabContent.querySelector('.order-stat-value:nth-of-type(3)')) {
                        let avgValue = 0;
                        if (tabData.last_24_hours_orders > 0) {
                            avgValue = (tabData.last_24_hours_value / tabData.last_24_hours_orders).toFixed(2);
                        }
                        tabContent.querySelector('.order-stat-value:nth-of-type(3)').textContent = avgValue;
                    }

                    // Update top product section if available
                    if (tabData.top_product) {
                        const topProduct = tabData.top_product;
                        const topProductSection = tabContent.querySelector('.top-product-section');

                        if (topProductSection) {
                            // Show the top product card
                            const noDataMessage = topProductSection.querySelector('.no-data-message');
                            if (noDataMessage) {
                                noDataMessage.style.display = 'none';
                            }

                            const topProductCard = topProductSection.querySelector('.top-product-card');
                            if (topProductCard) {
                                topProductCard.style.display = 'flex';

                                // Update product name
                                const productName = topProductCard.querySelector('.product-name');
                                if (productName) {
                                    productName.textContent = topProduct.name;
                                }

                                // Update product vendor
                                const productVendor = topProductCard.querySelector('.product-vendor');
                                if (productVendor) {
                                    productVendor.textContent = topProduct.vendor;
                                }

                                // Update product variant if available
                                const productVariant = topProductCard.querySelector('.product-variant');
                                if (productVariant && topProduct.variant) {
                                    productVariant.textContent = topProduct.variant;
                                    productVariant.style.display = 'inline';
                                } else if (productVariant) {
                                    productVariant.style.display = 'none';
                                }

                                // Update stats
                                const statValues = topProductCard.querySelectorAll('.stat-value');
                                if (statValues.length >= 4) {
                                    statValues[0].textContent = topProduct.quantity;
                                    statValues[1].textContent = topProduct.price;
                                    statValues[2].textContent = (topProduct.quantity * parseFloat(topProduct.price)).toFixed(2);

                                    // Update stock level with appropriate class
                                    statValues[3].textContent = topProduct.current_stock;
                                    statValues[3].className = 'stat-value';
                                    if (topProduct.current_stock < 5) {
                                        statValues[3].classList.add('low-stock');
                                    } else if (topProduct.current_stock < 10) {
                                        statValues[3].classList.add('medium-stock');
                                    } else {
                                        statValues[3].classList.add('good-stock');
                                    }
                                }
                            }
                        }
                    } else {
                        // No top product data available
                        const topProductSection = tabContent.querySelector('.top-product-section');
                        if (topProductSection) {
                            const topProductCard = topProductSection.querySelector('.top-product-card');
                            if (topProductCard) {
                                topProductCard.style.display = 'none';
                            }

                            const noDataMessage = topProductSection.querySelector('.no-data-message');
                            if (noDataMessage) {
                                noDataMessage.style.display = 'block';
                            }
                        }
                    }
                } else {
                    // Template doesn't exist, create content dynamically
                    const tabData = data.data;

                    // Create a basic structure for the Yesterday tab
                    tabContent.innerHTML = `
                        <div class="row">
                            <div class="col-md-12">
                                <div class="order-stats-container">
                                    <div class="order-stat-card">
                                        <div class="order-stat-icon">
                                            <i class="fas fa-shopping-cart" style="color: #4CAF50;"></i>
                                        </div>
                                        <div class="order-stat-content">
                                            <h3 class="order-stat-value">${tabData.last_24_hours_orders}</h3>
                                            <p class="order-stat-label">Total Orders</p>
                                        </div>
                                    </div>

                                    <div class="order-stat-card">
                                        <div class="order-stat-icon">
                                            <i class="fas fa-dollar-sign" style="color: #2196F3;"></i>
                                        </div>
                                        <div class="order-stat-content">
                                            <h3 class="order-stat-value">${tabData.last_24_hours_value.toFixed(2)}</h3>
                                            <p class="order-stat-label">Total Value</p>
                                        </div>
                                    </div>

                                    <div class="order-stat-card">
                                        <div class="order-stat-icon">
                                            <i class="fas fa-calculator" style="color: #FF9800;"></i>
                                        </div>
                                        <div class="order-stat-content">
                                            <h3 class="order-stat-value">
                                                ${tabData.last_24_hours_orders > 0
                                                    ? (tabData.last_24_hours_value / tabData.last_24_hours_orders).toFixed(2)
                                                    : '0'}
                                            </h3>
                                            <p class="order-stat-label">Average Order Value</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="top-product-section mt-4">
                                    <h5 class="section-title"><i class="fas fa-trophy me-2" style="color: #FFC107;"></i>Top Selling Product</h5>
                                    ${tabData.top_product
                                        ? `<div class="top-product-card">
                                            <div class="top-product-info">
                                                <h4 class="product-name">${tabData.top_product.name}</h4>
                                                <div class="product-details">
                                                    <span class="product-vendor">${tabData.top_product.vendor}</span>
                                                    ${tabData.top_product.variant
                                                        ? `<span class="product-variant">${tabData.top_product.variant}</span>`
                                                        : ''}
                                                </div>
                                            </div>
                                            <div class="top-product-stats">
                                                <div class="stat-item">
                                                    <span class="stat-value">${tabData.top_product.quantity}</span>
                                                    <span class="stat-label">Units Sold</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-value">${tabData.top_product.price}</span>
                                                    <span class="stat-label">Unit Price</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-value">${(tabData.top_product.quantity * parseFloat(tabData.top_product.price)).toFixed(2)}</span>
                                                    <span class="stat-label">Total Revenue</span>
                                                </div>
                                                <div class="stat-item stock-level">
                                                    <span class="stat-value ${tabData.top_product.current_stock < 5
                                                        ? 'low-stock'
                                                        : tabData.top_product.current_stock < 10
                                                            ? 'medium-stock'
                                                            : 'good-stock'}">
                                                        ${tabData.top_product.current_stock}
                                                    </span>
                                                    <span class="stat-label">Current Stock</span>
                                                </div>
                                            </div>
                                        </div>`
                                        : `<div class="no-data-message">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <span>No product sales data available for the last 24 hours.</span>
                                        </div>`
                                    }
                                </div>

                                <div class="alert alert-info mt-3 mb-0" style="background-color: rgba(33, 150, 243, 0.1); border-color: rgba(33, 150, 243, 0.2); color: #2196F3;">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <span>These statistics show your store's performance over the last 24 hours. Check back regularly to monitor your sales trends.</span>
                                </div>
                            </div>
                        </div>
                    `;
                }
            } else {
                throw new Error(data.message || 'Failed to load yesterday content');
            }
        })
        .catch(error => {
            console.error('Error loading yesterday tab:', error);
            // Fallback to a simple error message if API fails
            const tabContent = document.getElementById('yesterday-tab');

            if (tabContent) {
                // Create a basic error message
                tabContent.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span>Unable to load yesterday's data. Please try refreshing the page.</span>
                    </div>
                `;
            }
        });
}

/**
 * Initialize quick access section functionality
 */
function initQuickAccessSection() {
    // Add hover effects with animations
    const quickAccessCards = document.querySelectorAll('.quick-access-card');
    quickAccessCards.forEach(card => {
        // Add mouseenter event for subtle animation
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.quick-access-card-icon');
            icon.style.transition = 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
        });

        // Add click effect
        card.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('div');
            ripple.classList.add('ripple-effect');

            // Position the ripple at click coordinates
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;

            // Add ripple to card
            this.appendChild(ripple);

            // Remove ripple after animation completes
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * Initialize enterprise features
 * Note: This function is kept for backward compatibility but is no longer used
 * as the Enterprise Benefits tab has been removed
 */
function initEnterpriseFeatures() {
    // This function is no longer used
    console.log('Enterprise features are no longer available');
}

/**
 * Initialize dark mode select dropdowns
 * Note: This function is kept for backward compatibility but is no longer used
 * as the Enterprise Benefits tab has been removed
 */
function initDarkModeSelects() {
    // This function is no longer used
    console.log('Dark mode selects are no longer available');
}

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {string} type - The type of toast (info, success, error)
 */
function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.classList.add('toast-container');
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.classList.add('toast', `toast-${type}`);
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas ${type === 'info' ? 'fa-info-circle' : type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="toast-close"><i class="fas fa-times"></i></button>
    `;

    // Add toast to container
    toastContainer.appendChild(toast);

    // Add close button functionality
    const closeBtn = toast.querySelector('.toast-close');
    closeBtn.addEventListener('click', function() {
        toast.classList.add('toast-hiding');
        setTimeout(() => {
            toast.remove();
        }, 300);
    });

    // Auto-remove toast after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.classList.add('toast-hiding');
            setTimeout(() => {
                if (toast.parentNode) toast.remove();
            }, 300);
        }
    }, 5000);

    // Animate toast in
    setTimeout(() => {
        toast.classList.add('toast-visible');
    }, 10);
}